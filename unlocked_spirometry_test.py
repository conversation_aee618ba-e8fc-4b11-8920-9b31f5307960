#!/usr/bin/env python3
"""
Unlocked MIR Smart One Spirometry Test

This script uses the discovered unlock command (b'\x1b\x00') to unlock the device
and then properly initiate a spirometry test with real-time data capture.

BREAKTHROUGH: We discovered that b'\x1b\x00' unlocks the device!
Status changes from: 001bffff010000000000000000000000001a (locked)
                to: 0000000108343530343331363000000000a0 (unlocked)
"""

import asyncio
import time
import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from code.spirometry.mir_smart_one import MirSmartOne
    print("✅ Successfully imported MirSmartOne class")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class UnlockedSpirometer(MirSmartOne):
    """Enhanced spirometer with unlock capability."""
    
    def __init__(self, age=None, height_cm=None, gender=None, weight_kg=None):
        super().__init__(age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg, debug=True)
        
        # Known status patterns
        self.locked_status = bytes.fromhex("001bffff010000000000000000000000001a")
        self.unlocked_status = bytes.fromhex("0000000108343530343331363000000000a0")
        
        # Unlock command
        self.unlock_command = b'\x1b\x00'
        
        # Track notifications
        self.notification_count = 0
        self.test_data_received = False
        
    def fev1_handler(self, sender, data):
        """Enhanced FEV1 handler to track real data."""
        self.notification_count += 1
        print(f"🔔 FEV1 NOTIFICATION #{self.notification_count}: {data.hex()}")
        
        # Check if this looks like real data (not ffff)
        if data.hex() != "ffff" and len(data) > 0 and data[0] != 0:
            self.test_data_received = True
            print("🎉 REAL FEV1 DATA DETECTED!")
        
        super().fev1_handler(sender, data)
    
    def fvc_handler(self, sender, data):
        """Enhanced FVC handler to track real data."""
        self.notification_count += 1
        print(f"🔔 FVC NOTIFICATION #{self.notification_count}: {data.hex()}")
        
        if data.hex() != "0000" and len(data) > 0 and data[0] != 0:
            self.test_data_received = True
            print("🎉 REAL FVC DATA DETECTED!")
        
        super().fvc_handler(sender, data)
    
    def pef_handler(self, sender, data):
        """Enhanced PEF handler to track real data."""
        self.notification_count += 1
        print(f"🔔 PEF NOTIFICATION #{self.notification_count}: {data.hex()}")
        
        if len(data) > 0 and data[0] != 0:
            self.test_data_received = True
            print("🎉 REAL PEF DATA DETECTED!")
        
        super().pef_handler(sender, data)
    
    def status_handler(self, sender, data):
        """Enhanced status handler to track test phases."""
        self.notification_count += 1
        print(f"🔔 STATUS NOTIFICATION #{self.notification_count}: {data.hex()}")
        
        if len(data) >= 1:
            status = data[0]
            if status == 1:
                print("🚀 TEST STARTED (Status = 1)")
            elif status == 2:
                print("✅ TEST COMPLETED (Status = 2)")
        
        super().status_handler(sender, data)
    
    async def check_device_status(self, label=""):
        """Check and display current device status."""
        try:
            status = await self.client.read_gatt_char(self.status_char)
            print(f"📊 Status {label}: {status.hex()}")
            
            if status == self.locked_status:
                print("   🔒 Device is LOCKED")
                return "locked"
            elif status == self.unlocked_status:
                print("   🔓 Device is UNLOCKED")
                return "unlocked"
            else:
                print("   ❓ Device in unknown state")
                return "unknown"
                
        except Exception as e:
            print(f"❌ Error reading status: {e}")
            return "error"
    
    async def unlock_device(self):
        """Unlock the device using the discovered command."""
        print("\n🔓 UNLOCKING DEVICE")
        print("="*40)
        
        # Check current status
        current_state = await self.check_device_status("(before unlock)")
        
        if current_state == "unlocked":
            print("✅ Device already unlocked!")
            return True
        
        if current_state != "locked":
            print("⚠️ Device in unexpected state, attempting unlock anyway...")
        
        # Send unlock command
        print(f"🔑 Sending unlock command: {self.unlock_command.hex()}")
        try:
            await self.client.write_gatt_char(self.command_char, self.unlock_command)
            await asyncio.sleep(2)  # Wait for unlock to process
            
            # Check if unlock worked
            new_state = await self.check_device_status("(after unlock)")
            
            if new_state == "unlocked":
                print("🎉 DEVICE SUCCESSFULLY UNLOCKED!")
                return True
            else:
                print("❌ Unlock command failed")
                return False
                
        except Exception as e:
            print(f"❌ Unlock command failed: {e}")
            return False
    
    async def perform_unlocked_test(self):
        """Perform spirometry test on unlocked device."""
        print("\n🫁 PERFORMING UNLOCKED SPIROMETRY TEST")
        print("="*50)
        
        # Reset counters
        self.notification_count = 0
        self.test_data_received = False
        
        # Give user instructions
        print("\n📢 TEST INSTRUCTIONS:")
        print("🔹 Take the deepest breath you can")
        print("🔹 Seal lips tightly around mouthpiece")
        print("🔹 Blow out as HARD and FAST as possible")
        print("🔹 Continue until lungs are empty")
        print("🔹 Minimum 1 second blow duration")
        
        input("\nPress Enter when ready to start test...")
        
        # Send reset command first
        print("\n🔄 Resetting device state...")
        try:
            await self.client.write_gatt_char(self.command_char, b'\x00')
            await asyncio.sleep(1)
        except Exception as e:
            print(f"⚠️ Reset command failed: {e}")
        
        # Check status before test
        await self.check_device_status("(before test)")
        
        # Start test
        print("\n🚀 STARTING TEST...")
        print("💨 Blow NOW! As hard and fast as you can!")
        
        try:
            await self.client.write_gatt_char(self.command_char, b'\x01')
            print("✅ Test start command sent")
        except Exception as e:
            print(f"❌ Test start failed: {e}")
            return None
        
        # Monitor for notifications and status changes
        print("\n📡 Monitoring for real-time data...")
        print("Time | Notifications | Real Data | Status")
        print("-" * 45)
        
        start_time = time.time()
        last_notification_count = 0
        
        for i in range(30):  # Monitor for 30 seconds
            await asyncio.sleep(1)
            elapsed = time.time() - start_time
            
            # Check for new notifications
            new_notifications = self.notification_count - last_notification_count
            last_notification_count = self.notification_count
            
            # Status indicators
            data_status = "YES" if self.test_data_received else "NO"
            notif_status = f"+{new_notifications}" if new_notifications > 0 else "0"
            
            print(f"{elapsed:4.0f}s | {self.notification_count:13d} | {data_status:9s} | {notif_status}")
            
            # Check device status periodically
            if int(elapsed) % 5 == 0:
                await self.check_device_status(f"(at {elapsed:.0f}s)")
            
            # Break if test completed
            if self.test_complete:
                print(f"\n✅ Test completed at {elapsed:.1f}s")
                break
        
        # Final status check
        await self.check_device_status("(final)")
        
        # Get results
        print("\n📊 RETRIEVING RESULTS...")
        results = await self.get_results()
        
        return results


async def run_unlocked_spirometry_test(age=None, height_cm=None, gender=None, weight_kg=None):
    """Main function to run unlocked spirometry test."""
    print("🔓 Unlocked MIR Smart One Spirometry Test")
    print("="*60)
    print("Using the discovered unlock command to access real device data!")
    print()
    
    spirometer = UnlockedSpirometer(age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg)
    
    try:
        # Connect
        print("🔗 Connecting to device...")
        await spirometer.connect()
        
        # Set up notifications
        print("📡 Setting up notifications...")
        await spirometer.setup_notifications()
        
        # Unlock device
        unlock_success = await spirometer.unlock_device()
        
        if not unlock_success:
            print("❌ Failed to unlock device. Cannot proceed with test.")
            return None
        
        # Perform test
        results = await spirometer.perform_unlocked_test()
        
        # Display results
        if results:
            print("\n" + "="*60)
            print("🎉 UNLOCKED SPIROMETRY RESULTS")
            print("="*60)
            print(f"📊 PEF: {results['PEF']} L/s")
            print(f"📊 FEV1: {results['FEV1']} L")
            print(f"📊 FVC: {results['FVC']} L")
            print(f"📊 FEV1/FVC: {results['FEV1/FVC']}%")
            
            if 'blow_duration' in results:
                duration = results['blow_duration']
                duration_status = "✅ Good" if duration >= 1.0 else "⚠️ Short"
                print(f"⏱️ Blow Duration: {duration}s ({duration_status})")
            
            # Show interpretation if available
            if 'interpretation' in results:
                interp = results['interpretation']
                print(f"\n🔍 Clinical Interpretation:")
                
                if 'zones' in interp:
                    zones = interp['zones']
                    for param, zone in zones.items():
                        icon = {'Green': '🟢', 'Yellow': '🟡', 'Red': '🔴'}.get(zone, '⚪')
                        print(f"   {param}: {icon} {zone}")
                
                if 'overall_assessment' in interp:
                    print(f"\n🏥 Overall: {interp['overall_assessment']}")
            
            print(f"\n📈 Data Quality:")
            print(f"   Notifications received: {spirometer.notification_count}")
            print(f"   Real data detected: {'Yes' if spirometer.test_data_received else 'No'}")
            
        else:
            print("❌ No results obtained")
        
        # Disconnect
        await spirometer.disconnect()
        
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    print("🔓 Unlocked MIR Smart One Spirometry Test")
    print("This script uses the breakthrough unlock discovery!")
    print()
    
    # Get demographics
    print("👤 Patient Demographics (optional):")
    try:
        age_input = input("Age (years, or Enter to skip): ").strip()
        age = int(age_input) if age_input else None
        
        height_input = input("Height (cm, or Enter to skip): ").strip()
        height_cm = float(height_input) if height_input else None
        
        gender_input = input("Gender (male/female, or Enter to skip): ").strip().lower()
        gender = gender_input if gender_input in ['male', 'female'] else None
        
        weight_input = input("Weight (kg, or Enter to skip): ").strip()
        weight_kg = float(weight_input) if weight_input else None
        
    except ValueError:
        print("Using test without demographics")
        age = height_cm = gender = weight_kg = None
    
    try:
        results = asyncio.run(run_unlocked_spirometry_test(
            age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg))
        
        if results:
            print("\n🎉 SUCCESS! Device unlocked and test completed!")
        else:
            print("\n❌ Test failed despite unlock")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test cancelled")
    
    print("\n" + "="*60)
    print("🎯 UNLOCKED TEST COMPLETE")
    print("="*60)
    print("The device unlock breakthrough opens up full spirometry functionality!")
