#!/usr/bin/env python3
"""
Raw BLE Data Debug Script for MIR Smart One Spirometer

This script captures and analyzes the raw byte stream from the MIR Smart One device
to understand the actual data format being transmitted. The device likely sends
raw sensor readings as a stream of bytes rather than pre-calculated values.

Key Focus Areas:
1. Capture all raw hex data from BLE notifications
2. Analyze byte patterns during different phases of the test
3. Identify which bytes correspond to which measurements
4. Determine correct scaling factors for raw sensor data
"""

import asyncio
import struct
import time
import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from code.spirometry.mir_smart_one import MirSmartOne
    print("✅ Successfully imported MirSmartOne class")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class RawDataLogger(MirSmartOne):
    """Enhanced MirSmartOne class with comprehensive raw data logging."""
    
    def __init__(self):
        super().__init__(debug=True)
        self.raw_data_log = []
        self.test_phase = "idle"
        self.start_time = None
        
    def log_raw_data(self, characteristic, data, timestamp=None):
        """Log raw data with timestamp and test phase."""
        if timestamp is None:
            timestamp = time.time()
        
        relative_time = 0
        if self.start_time:
            relative_time = timestamp - self.start_time
            
        log_entry = {
            'timestamp': timestamp,
            'relative_time': relative_time,
            'phase': self.test_phase,
            'characteristic': characteristic,
            'hex_data': data.hex(),
            'raw_bytes': list(data),
            'length': len(data)
        }
        
        self.raw_data_log.append(log_entry)
        
        # Print immediately for real-time monitoring
        print(f"[{relative_time:6.2f}s] [{self.test_phase:8s}] {characteristic:8s}: {data.hex()} ({len(data)} bytes) = {list(data)}")
    
    def fev1_handler(self, sender, data):
        """Enhanced FEV1 handler with raw data logging."""
        self.log_raw_data("FEV1", data)
        
        # Try multiple interpretations
        print(f"[DEBUG] FEV1 raw data: {data.hex()} ({len(data)} bytes)")
        
        if len(data) == 1:
            byte_val = data[0]
            print(f"[DEBUG] FEV1 as byte: {byte_val}")
            print(f"[DEBUG] Scaled options: raw={byte_val}, /10={byte_val/10:.3f}, /100={byte_val/100:.3f}")
            
        elif len(data) == 2:
            int_val = struct.unpack('<H', data)[0]
            print(f"[DEBUG] FEV1 as uint16: {int_val} (hex: {data.hex()})")
            
            if data.hex() == "ffff":
                print("[DEBUG] ⚠️  FEV1 received error signal (0xFFFF)")
            else:
                print(f"[DEBUG] Scaled options: raw={int_val}, /10={int_val/10:.3f}, /100={int_val/100:.3f}, /1000={int_val/1000:.3f}")
                
        elif len(data) == 4:
            # Try as float and int
            try:
                float_val = struct.unpack('<f', data)[0]
                print(f"[DEBUG] FEV1 as float: {float_val}")
            except:
                pass
            try:
                int_val = struct.unpack('<I', data)[0]
                print(f"[DEBUG] FEV1 as uint32: {int_val}")
            except:
                pass
        
        # Call parent method for normal processing
        super().fev1_handler(sender, data)
    
    def fvc_handler(self, sender, data):
        """Enhanced FVC handler with raw data logging."""
        self.log_raw_data("FVC", data)
        
        print(f"[DEBUG] FVC raw data: {data.hex()} ({len(data)} bytes)")
        
        if len(data) == 1:
            byte_val = data[0]
            print(f"[DEBUG] FVC as byte: {byte_val}")
            print(f"[DEBUG] Scaled options: raw={byte_val}, /10={byte_val/10:.3f}, /100={byte_val/100:.3f}")
            
        elif len(data) == 2:
            int_val = struct.unpack('<H', data)[0]
            print(f"[DEBUG] FVC as uint16: {int_val} (hex: {data.hex()})")
            print(f"[DEBUG] Scaled options: raw={int_val}, /10={int_val/10:.3f}, /100={int_val/100:.3f}, /1000={int_val/1000:.3f}")
        
        super().fvc_handler(sender, data)
    
    def pef_handler(self, sender, data):
        """Enhanced PEF handler with raw data logging."""
        self.log_raw_data("PEF", data)
        
        print(f"[DEBUG] PEF raw data: {data.hex()} ({len(data)} bytes)")
        
        if len(data) == 1:
            byte_val = data[0]
            print(f"[DEBUG] PEF as byte: {byte_val}")
            print(f"[DEBUG] Scaled options: raw={byte_val}, /10={byte_val/10:.3f}, /100={byte_val/100:.3f}")
            # PEF is often higher, so might need less scaling
            print(f"[DEBUG] PEF realistic range: raw={byte_val} L/s, *10={byte_val*10} L/min")
            
        elif len(data) == 2:
            int_val = struct.unpack('<H', data)[0]
            print(f"[DEBUG] PEF as uint16: {int_val} (hex: {data.hex()})")
            print(f"[DEBUG] Scaled options: raw={int_val}, /10={int_val/10:.3f}, /100={int_val/100:.3f}")
            print(f"[DEBUG] PEF as L/min: raw={int_val}, /10={int_val/10:.1f}")
        
        super().pef_handler(sender, data)
    
    def ratio_handler(self, sender, data):
        """Enhanced ratio handler with raw data logging."""
        self.log_raw_data("RATIO", data)
        
        print(f"[DEBUG] FEV1/FVC raw data: {data.hex()} ({len(data)} bytes)")
        
        if len(data) == 1:
            byte_val = data[0]
            print(f"[DEBUG] Ratio as byte: {byte_val}")
            print(f"[DEBUG] As percentage: {byte_val}% (if direct), {byte_val/10:.1f}% (if scaled)")
            
        elif len(data) == 2:
            int_val = struct.unpack('<H', data)[0]
            print(f"[DEBUG] Ratio as uint16: {int_val} (hex: {data.hex()})")
            print(f"[DEBUG] As percentage: {int_val}% (if direct), {int_val/10:.1f}% (if scaled)")
        
        super().ratio_handler(sender, data)
    
    def status_handler(self, sender, data):
        """Enhanced status handler with raw data logging."""
        self.log_raw_data("STATUS", data)
        
        print(f"[DEBUG] Status raw data: {data.hex()} ({len(data)} bytes)")
        
        if len(data) >= 1:
            status = data[0]
            print(f"[DEBUG] Status byte: {status}")
            
            # Update test phase based on status
            if status == 0:
                self.test_phase = "idle"
            elif status == 1:
                self.test_phase = "ready"
                if not self.start_time:
                    self.start_time = time.time()
                    print("[DEBUG] 🚀 Test timing started")
            elif status == 2:
                self.test_phase = "complete"
                print("[DEBUG] ✅ Test marked complete")
            else:
                self.test_phase = f"status_{status}"
        
        super().status_handler(sender, data)
    
    def print_data_summary(self):
        """Print summary of all captured data."""
        print("\n" + "="*80)
        print("📊 RAW DATA CAPTURE SUMMARY")
        print("="*80)
        
        print(f"Total data points captured: {len(self.raw_data_log)}")
        
        # Group by characteristic
        by_char = {}
        for entry in self.raw_data_log:
            char = entry['characteristic']
            if char not in by_char:
                by_char[char] = []
            by_char[char].append(entry)
        
        for char, entries in by_char.items():
            print(f"\n{char} ({len(entries)} entries):")
            
            # Show unique hex patterns
            unique_patterns = set(entry['hex_data'] for entry in entries)
            print(f"  Unique hex patterns: {sorted(unique_patterns)}")
            
            # Show value ranges if numeric
            if entries:
                try:
                    if len(entries[0]['raw_bytes']) == 1:
                        values = [entry['raw_bytes'][0] for entry in entries]
                        print(f"  Byte value range: {min(values)} - {max(values)}")
                    elif len(entries[0]['raw_bytes']) == 2:
                        values = [struct.unpack('<H', bytes(entry['raw_bytes']))[0] for entry in entries]
                        print(f"  Uint16 value range: {min(values)} - {max(values)}")
                except:
                    pass
        
        print("\n" + "="*80)


async def capture_raw_ble_data():
    """Main function to capture raw BLE data from spirometry test."""
    print("🔍 MIR Smart One Raw BLE Data Capture")
    print("="*60)
    print("This script will capture ALL raw BLE data during a spirometry test.")
    print("Focus: Understanding the actual byte stream from the device.")
    print()
    
    spirometer = RawDataLogger()
    
    try:
        print("Connecting to device...")
        await spirometer.connect()
        
        # Dump characteristics first
        await spirometer.dump_all_characteristics()
        
        print("\nSetting up notifications...")
        await spirometer.setup_notifications()
        
        print("\nReady to capture data!")
        print("Follow the test instructions and blow with full effort.")
        print("All raw BLE data will be logged in real-time.")
        print()
        
        input("Press Enter to start test...")
        
        # Start the test
        spirometer.test_phase = "starting"
        await spirometer.start_test()
        
        # Monitor for data
        print("\n🔄 Monitoring BLE data stream...")
        print("Time    | Phase    | Char     | Hex Data        | Bytes")
        print("-" * 60)
        
        # Wait for test completion or timeout
        timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            await asyncio.sleep(0.5)
            
            if spirometer.test_complete:
                print("\n✅ Test completed via status notification")
                break
        
        # Wait a bit more for any final data
        await asyncio.sleep(3)
        
        # Print summary
        spirometer.print_data_summary()
        
        # Disconnect
        await spirometer.disconnect()
        
        return spirometer.raw_data_log
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    print("🧪 Raw BLE Data Capture Tool")
    print("This captures the actual byte stream from the MIR Smart One device")
    print()
    
    try:
        data_log = asyncio.run(capture_raw_ble_data())
        
        if data_log:
            print(f"\n📝 Captured {len(data_log)} data points")
            print("\n🔍 ANALYSIS TIPS:")
            print("1. Look for patterns in the hex data")
            print("2. Note which characteristics send data during blowing")
            print("3. Check if values increase during the blow")
            print("4. Identify realistic scaling factors")
            print("5. Look for error patterns like 'ffff'")
            
    except KeyboardInterrupt:
        print("\n⏹️  Capture cancelled")
