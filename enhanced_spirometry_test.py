#!/usr/bin/env python3
"""
Enhanced MIR Smart One Spirometry Test

This script builds on the unlock breakthrough to capture full spirometry data.
Focus areas:
1. Multi-attempt testing with proper timing
2. Enhanced blow instructions and countdown
3. Alternate priming command sequences
4. Raw sensor data capture and logging
5. CSV logging for analysis
"""

import asyncio
import time
import csv
import sys
import os
from datetime import datetime

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from code.spirometry.mir_smart_one import MirSmartOne
    print("✅ Successfully imported MirSmartOne class")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class EnhancedSpirometer(MirSmartOne):
    """Enhanced spirometer with multi-attempt testing and data logging."""
    
    def __init__(self, age=None, height_cm=None, gender=None, weight_kg=None):
        super().__init__(age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg, debug=True)
        
        # Unlock command
        self.unlock_command = b'\x1b\x00'
        
        # Data logging
        self.csv_filename = f"spirometry_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.notification_log = []
        
        # Test tracking
        self.current_attempt = 0
        self.best_results = None
        self.test_start_time = None
        
    def log_notification(self, char_name, data, timestamp=None):
        """Log all notifications to CSV for analysis."""
        if timestamp is None:
            timestamp = time.time()
        
        relative_time = 0
        if self.test_start_time:
            relative_time = timestamp - self.test_start_time
        
        entry = {
            'timestamp': timestamp,
            'relative_time': relative_time,
            'attempt': self.current_attempt,
            'characteristic': char_name,
            'hex_data': data.hex(),
            'raw_bytes': list(data),
            'length': len(data)
        }
        
        self.notification_log.append(entry)
        
        # Real-time display
        print(f"[{relative_time:6.2f}s] {char_name:8s}: {data.hex()} = {list(data)}")
    
    def fev1_handler(self, sender, data):
        """Enhanced FEV1 handler with logging."""
        self.log_notification("FEV1", data)
        
        if data.hex() != "ffff":
            print("🎉 FEV1 DATA RECEIVED (not error signal)!")
        
        super().fev1_handler(sender, data)
    
    def fvc_handler(self, sender, data):
        """Enhanced FVC handler with logging."""
        self.log_notification("FVC", data)
        super().fvc_handler(sender, data)
    
    def pef_handler(self, sender, data):
        """Enhanced PEF handler with logging."""
        self.log_notification("PEF", data)
        super().pef_handler(sender, data)
    
    def ratio_handler(self, sender, data):
        """Enhanced ratio handler with logging."""
        self.log_notification("RATIO", data)
        super().ratio_handler(sender, data)
    
    def status_handler(self, sender, data):
        """Enhanced status handler with logging."""
        self.log_notification("STATUS", data)
        
        if len(data) >= 1:
            status = data[0]
            if status == 1:
                print("🚀 TEST PHASE: ACTIVE MEASUREMENT")
            elif status == 2:
                print("✅ TEST PHASE: COMPLETED")
        
        super().status_handler(sender, data)
    
    async def unlock_device(self):
        """Unlock the device using the discovered command."""
        print("\n🔓 UNLOCKING DEVICE")
        print("="*40)
        
        try:
            await self.client.write_gatt_char(self.command_char, self.unlock_command)
            await asyncio.sleep(2)
            
            status = await self.client.read_gatt_char(self.status_char)
            print(f"📊 Unlocked status: {status.hex()}")
            
            # Check if device serial is visible (indicates unlock success)
            if b'043' in status:  # Part of device serial "0433160"
                print("🎉 DEVICE SUCCESSFULLY UNLOCKED!")
                return True
            else:
                print("❌ Unlock may have failed")
                return False
                
        except Exception as e:
            print(f"❌ Unlock failed: {e}")
            return False
    
    async def prime_device_for_test(self):
        """Prime device with alternate command sequence."""
        print("\n🔧 PRIMING DEVICE FOR TEST")
        print("="*40)
        
        # Sequence: Get last result → Clear → Reset → Ready
        priming_sequence = [
            (b'\x02', "Get last result"),
            (b'\x00', "Clear state"),
            (b'\x00', "Reset again"),
        ]
        
        for command, description in priming_sequence:
            print(f"🔧 {description}: {command.hex()}")
            try:
                await self.client.write_gatt_char(self.command_char, command)
                await asyncio.sleep(1)
            except Exception as e:
                print(f"   ⚠️ {description} failed: {e}")
        
        print("✅ Device priming complete")
    
    async def countdown_and_blow_instructions(self):
        """Provide clear countdown and blow instructions."""
        print("\n" + "="*60)
        print("🫁 SPIROMETRY TEST INSTRUCTIONS")
        print("="*60)
        print("🔹 Stand or sit upright with good posture")
        print("🔹 Take the DEEPEST breath you can")
        print("🔹 Seal your lips TIGHTLY around the mouthpiece")
        print("🔹 Blow out as HARD and FAST as possible")
        print("🔹 Continue blowing until your lungs are COMPLETELY empty")
        print("🔹 Minimum blow time: 3 seconds for accurate results")
        print("🔹 Think: 'Empty a balloon as fast as possible'")
        print("="*60)
        
        input("Press Enter when you're ready and have the mouthpiece in position...")
        
        print("\n⏱️ GET READY TO BLOW...")
        for i in range(5, 0, -1):
            print(f"   {i}...")
            await asyncio.sleep(1)
        
        print("\n💨💨💨 BLOW NOW! AS HARD AS YOU CAN! 💨💨💨")
        print("Keep blowing... keep blowing... keep blowing...")
    
    async def perform_single_test_attempt(self, attempt_num):
        """Perform a single test attempt with full monitoring."""
        print(f"\n🧪 TEST ATTEMPT #{attempt_num}")
        print("="*50)
        
        self.current_attempt = attempt_num
        
        # Prime device
        await self.prime_device_for_test()
        
        # Clear previous data
        self.fev1_value = 0
        self.fvc_value = 0
        self.pef_value = 0
        self.ratio_value = 0
        self.test_complete = False
        self.test_started = False
        
        # Countdown and instructions
        await self.countdown_and_blow_instructions()
        
        # Start test and timing
        self.test_start_time = time.time()
        
        try:
            await self.client.write_gatt_char(self.command_char, b'\x01')
            print("✅ Test start command sent")
        except Exception as e:
            print(f"❌ Test start failed: {e}")
            return None
        
        # Monitor for 15 seconds with real-time feedback
        print("\n📡 MONITORING TEST DATA...")
        print("Time  | FEV1 | FVC  | PEF  | Status")
        print("-" * 40)
        
        for i in range(15):
            await asyncio.sleep(1)
            elapsed = time.time() - self.test_start_time
            
            # Show current values
            fev1_display = f"{self.fev1_value:.2f}" if self.fev1_value != 0.05 else "ERR"
            fvc_display = f"{self.fvc_value:.2f}"
            pef_display = f"{self.pef_value:.2f}"
            status_display = "DONE" if self.test_complete else "RUN"
            
            print(f"{elapsed:4.0f}s | {fev1_display:4s} | {fvc_display:4s} | {pef_display:4s} | {status_display}")
            
            # Check if we got good data
            if self.fev1_value > 0.5 and self.fvc_value > 1.0 and self.pef_value > 2.0:
                print("🎉 GOOD DATA DETECTED! Test successful!")
                break
            
            if self.test_complete:
                print("✅ Test marked complete by device")
                break
        
        # Get final results
        await asyncio.sleep(2)  # Let final notifications arrive
        results = await self.get_results()
        
        return results
    
    async def save_notification_log(self):
        """Save all notifications to CSV file."""
        if not self.notification_log:
            return
        
        print(f"\n💾 Saving notification log to {self.csv_filename}")
        
        with open(self.csv_filename, 'w', newline='') as csvfile:
            fieldnames = ['timestamp', 'relative_time', 'attempt', 'characteristic', 
                         'hex_data', 'raw_bytes', 'length']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for entry in self.notification_log:
                writer.writerow(entry)
        
        print(f"✅ Saved {len(self.notification_log)} notifications to CSV")
    
    async def analyze_results(self, all_results):
        """Analyze results from multiple attempts."""
        print("\n📊 MULTI-ATTEMPT ANALYSIS")
        print("="*50)
        
        if not all_results:
            print("❌ No successful results to analyze")
            return
        
        # Find best attempt (highest FVC + FEV1)
        best_attempt = 0
        best_score = 0
        
        for i, result in enumerate(all_results):
            if result:
                score = result.get('FVC', 0) + result.get('FEV1', 0)
                if score > best_score:
                    best_score = score
                    best_attempt = i
                    self.best_results = result
        
        print(f"📈 Results Summary:")
        for i, result in enumerate(all_results):
            if result:
                marker = "🏆" if i == best_attempt else "📊"
                print(f"{marker} Attempt {i+1}: FEV1={result.get('FEV1', 0):.2f}L, "
                      f"FVC={result.get('FVC', 0):.2f}L, PEF={result.get('PEF', 0):.2f}L/s")
            else:
                print(f"❌ Attempt {i+1}: Failed")
        
        if self.best_results:
            print(f"\n🏆 BEST RESULTS (Attempt {best_attempt + 1}):")
            print(f"   PEF: {self.best_results['PEF']:.2f} L/s ({self.best_results['PEF']*60:.0f} L/min)")
            print(f"   FEV1: {self.best_results['FEV1']:.2f} L")
            print(f"   FVC: {self.best_results['FVC']:.2f} L")
            print(f"   FEV1/FVC: {self.best_results['FEV1/FVC']:.1f}%")
            
            # Quality assessment
            if self.best_results['FEV1'] > 2.0 and self.best_results['FVC'] > 3.0:
                print("✅ EXCELLENT: Results look realistic!")
            elif self.best_results['FEV1'] > 1.0 and self.best_results['FVC'] > 2.0:
                print("⚠️ MODERATE: Results are improving but may need stronger blow")
            else:
                print("❌ POOR: Results still too low - try blowing harder and longer")


async def run_enhanced_spirometry_test(age=None, height_cm=None, gender=None, weight_kg=None, num_attempts=3):
    """Run enhanced multi-attempt spirometry test."""
    print("🚀 Enhanced MIR Smart One Spirometry Test")
    print("="*60)
    print("Multi-attempt testing with enhanced blow detection!")
    print()
    
    spirometer = EnhancedSpirometer(age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg)
    
    try:
        # Connect and setup
        print("🔗 Connecting to device...")
        await spirometer.connect()
        
        print("📡 Setting up notifications...")
        await spirometer.setup_notifications()
        
        # Unlock device
        unlock_success = await spirometer.unlock_device()
        if not unlock_success:
            print("❌ Failed to unlock device")
            return None
        
        # Perform multiple test attempts
        all_results = []
        
        for attempt in range(1, num_attempts + 1):
            print(f"\n{'='*60}")
            print(f"🧪 STARTING ATTEMPT {attempt} of {num_attempts}")
            print(f"{'='*60}")
            
            results = await spirometer.perform_single_test_attempt(attempt)
            all_results.append(results)
            
            if attempt < num_attempts:
                print(f"\n⏸️ Rest for 10 seconds before next attempt...")
                await asyncio.sleep(10)
        
        # Analyze all results
        await spirometer.analyze_results(all_results)
        
        # Save data log
        await spirometer.save_notification_log()
        
        # Disconnect
        await spirometer.disconnect()
        
        return spirometer.best_results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    print("🚀 Enhanced MIR Smart One Spirometry Test")
    print("Multi-attempt testing with proper blow technique!")
    print()
    
    # Get demographics
    print("👤 Patient Demographics (for interpretation):")
    try:
        age_input = input("Age (years, or Enter to skip): ").strip()
        age = int(age_input) if age_input else None
        
        height_input = input("Height (cm, or Enter to skip): ").strip()
        height_cm = float(height_input) if height_input else None
        
        gender_input = input("Gender (male/female, or Enter to skip): ").strip().lower()
        gender = gender_input if gender_input in ['male', 'female'] else None
        
        weight_input = input("Weight (kg, or Enter to skip): ").strip()
        weight_kg = float(weight_input) if weight_input else None
        
        attempts_input = input("Number of test attempts (1-5, default 3): ").strip()
        num_attempts = int(attempts_input) if attempts_input and attempts_input.isdigit() else 3
        num_attempts = max(1, min(5, num_attempts))
        
    except ValueError:
        print("Using defaults")
        age = height_cm = gender = weight_kg = None
        num_attempts = 3
    
    try:
        results = asyncio.run(run_enhanced_spirometry_test(
            age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg, 
            num_attempts=num_attempts))
        
        if results:
            print("\n🎉 ENHANCED TEST COMPLETED SUCCESSFULLY!")
            print("Check the CSV file for detailed notification analysis.")
        else:
            print("\n❌ Test failed - check blow technique and device connection")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test cancelled")
    
    print("\n" + "="*60)
    print("🎯 ENHANCED TEST COMPLETE")
    print("="*60)
    print("💡 Tips for better results:")
    print("• Blow HARDER and FASTER (like blowing up a balloon quickly)")
    print("• Continue blowing for at least 3 seconds")
    print("• Ensure tight seal around mouthpiece")
    print("• Take the deepest breath possible before blowing")
    print("• Review the CSV log for detailed sensor data analysis")
