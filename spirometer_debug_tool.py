#!/usr/bin/env python3
"""
MIR Smart One Spirometer Debug Tool
==================================

A comprehensive tool for debugging and testing the MIR Smart One spirometer.
Combines all essential functionality in one script:

1. Device connection and unlock
2. Raw BLE data capture with CSV logging
3. Real-time data visualization
4. Spirometry test execution
5. Data analysis and plotting

Usage:
    python spirometer_debug_tool.py [mode]

Modes:
    test        - Run a complete spirometry test (default)
    debug       - Capture raw BLE data with CSV logging
    monitor     - Monitor device status and characteristics
    plot        - Plot data from existing CSV files
"""

import asyncio
import struct
import time
import sys
import os
import csv
import json
import glob
from datetime import datetime

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from code.spirometry.mir_smart_one import MirSmartOne
    print("✅ Successfully imported MirSmartOne class")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure the code/spirometry/mir_smart_one.py file exists")
    sys.exit(1)


class ComprehensiveSpirometer(MirSmartOne):
    """Enhanced spirometer class with all debugging capabilities."""

    def __init__(self, enable_csv=False):
        super().__init__(debug=True)
        self.enable_csv = enable_csv
        self.raw_data_log = []
        self.test_phase = "idle"
        self.start_time = None
        self.csv_file = None
        self.csv_writer = None

        # All characteristics we want to monitor
        self.all_chars = {
            self.fev1_char: "FEV1",
            self.fvc_char: "FVC",
            self.pef_char: "PEF",
            self.fev1_fvc_char: "RATIO",
            self.status_char: "STATUS",
            self.device_info_char: "DEVICE_INFO",
            self.battery_char: "BATTERY_ALT"
        }

    def init_csv_logging(self):
        """Initialize CSV logging."""
        if not self.enable_csv:
            return True

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"spirometer_data_{timestamp}.csv"

        try:
            self.csv_file = open(csv_filename, 'w', newline='')
            self.csv_writer = csv.writer(self.csv_file)

            header = [
                'timestamp', 'relative_time', 'phase', 'characteristic', 'char_name',
                'hex_data', 'length', 'byte_0', 'byte_1', 'byte_2', 'byte_3',
                'uint16_le', 'uint32_le', 'float_le', 'scaled_div10', 'scaled_div100'
            ]
            self.csv_writer.writerow(header)
            self.csv_file.flush()

            print(f"📝 CSV logging enabled: {csv_filename}")
            return True

        except Exception as e:
            print(f"❌ CSV logging failed: {e}")
            return False

    def log_data(self, characteristic, data):
        """Log data with all interpretations."""
        if not self.start_time:
            self.start_time = time.time()

        timestamp = time.time()
        relative_time = timestamp - self.start_time
        char_name = self.all_chars.get(characteristic, "UNKNOWN")

        # Parse data with multiple interpretations
        data_bytes = list(data)
        padded_bytes = data_bytes + [0] * (4 - len(data_bytes))

        uint16_le = uint32_le = float_le = None

        try:
            if len(data_bytes) >= 2:
                uint16_le = struct.unpack('<H', bytes(data_bytes[:2]))[0]
            if len(data_bytes) >= 4:
                uint32_le = struct.unpack('<I', bytes(data_bytes[:4]))[0]
                float_le = struct.unpack('<f', bytes(data_bytes[:4]))[0]
        except:
            pass

        first_byte = data_bytes[0] if data_bytes else 0

        log_entry = {
            'timestamp': timestamp,
            'relative_time': relative_time,
            'phase': self.test_phase,
            'characteristic': characteristic,
            'char_name': char_name,
            'hex_data': data.hex(),
            'length': len(data),
            'raw_bytes': data_bytes,
            'uint16_le': uint16_le,
            'uint32_le': uint32_le,
            'float_le': float_le,
            'scaled_div10': first_byte / 10.0,
            'scaled_div100': first_byte / 100.0
        }

        self.raw_data_log.append(log_entry)

        # Print real-time data with more detail for potential flow data
        if char_name in ["DEVICE_INFO", "BATTERY_ALT"]:
            # These might be turbine data - show more interpretations
            interpretations = []
            if uint16_le is not None:
                interpretations.append(f"uint16:{uint16_le}")
                # Show realistic flow scaling for uint16
                interpretations.append(f"÷100:{uint16_le/100:.3f}L/s")
                interpretations.append(f"÷1000:{uint16_le/1000:.3f}L/s")

            interp_str = " | ".join(interpretations) if interpretations else ""
            print(
                f"[{relative_time:6.2f}s] {char_name:10s}: {data.hex():8s} = {data_bytes} | {interp_str}")
        else:
            print(
                f"[{relative_time:6.2f}s] {char_name:10s}: {data.hex():8s} = {data_bytes}")

        # Write to CSV
        if self.csv_writer:
            row = [
                timestamp, relative_time, self.test_phase, characteristic, char_name,
                data.hex(), len(data), padded_bytes[0], padded_bytes[1],
                padded_bytes[2], padded_bytes[3], uint16_le, uint32_le,
                float_le, first_byte/10.0, first_byte/100.0
            ]
            self.csv_writer.writerow(row)
            self.csv_file.flush()

    # Override all handlers to use unified logging
    def fev1_handler(self, sender, data):
        self.log_data(self.fev1_char, data)
        super().fev1_handler(sender, data)

    def fvc_handler(self, sender, data):
        self.log_data(self.fvc_char, data)
        super().fvc_handler(sender, data)

    def pef_handler(self, sender, data):
        self.log_data(self.pef_char, data)
        super().pef_handler(sender, data)

    def ratio_handler(self, sender, data):
        self.log_data(self.fev1_fvc_char, data)
        super().ratio_handler(sender, data)

    def status_handler(self, sender, data):
        self.log_data(self.status_char, data)

        # Update test phase
        if len(data) >= 1:
            status = data[0]
            if status == 0:
                self.test_phase = "idle"
            elif status == 1:
                self.test_phase = "ready"
            elif status == 2:
                self.test_phase = "complete"
            else:
                self.test_phase = f"status_{status}"

        super().status_handler(sender, data)

    def device_info_handler(self, sender, data):
        """Handle device info notifications (potential turbine data)."""
        self.log_data(self.device_info_char, data)

    def battery_alt_handler(self, sender, data):
        """Handle alternative battery notifications (potential turbine data)."""
        self.log_data(self.battery_char, data)

    async def setup_all_notifications(self):
        """Set up notifications for all characteristics."""
        print("Setting up all notifications...")

        # Standard spirometry notifications
        await self.setup_notifications()

        # Additional characteristics that might contain turbine data
        for char_uuid, char_name in [(self.device_info_char, "DEVICE_INFO"),
                                     (self.battery_char, "BATTERY_ALT")]:
            try:
                if char_name == "DEVICE_INFO":
                    await self.client.start_notify(char_uuid, self.device_info_handler)
                elif char_name == "BATTERY_ALT":
                    await self.client.start_notify(char_uuid, self.battery_alt_handler)
                print(f"✅ {char_name} notifications enabled")
            except Exception as e:
                print(f"⚠️  {char_name} notifications failed: {e}")

    def close_csv(self):
        """Close CSV file."""
        if self.csv_file:
            self.csv_file.close()
            print(f"📁 CSV file saved")

    def print_summary(self):
        """Print data capture summary."""
        if not self.raw_data_log:
            print("No data captured")
            return

        print(f"\n📊 CAPTURE SUMMARY")
        print("="*40)
        print(f"Total data points: {len(self.raw_data_log)}")

        # Group by characteristic
        by_char = {}
        for entry in self.raw_data_log:
            char = entry['char_name']
            if char not in by_char:
                by_char[char] = []
            by_char[char].append(entry)

        for char, entries in by_char.items():
            print(f"{char}: {len(entries)} notifications")
            if entries:
                # Show value ranges
                try:
                    first_bytes = [e['raw_bytes'][0]
                                   for e in entries if e['raw_bytes']]
                    if first_bytes:
                        print(
                            f"  Range: {min(first_bytes)} - {max(first_bytes)}")
                except:
                    pass


async def run_spirometry_test():
    """Run a complete spirometry test."""
    print("🫁 SPIROMETRY TEST MODE")
    print("="*30)

    spirometer = ComprehensiveSpirometer(enable_csv=True)

    try:
        # Connect and setup
        print("Connecting to device...")
        await spirometer.connect()

        spirometer.init_csv_logging()
        await spirometer.setup_all_notifications()

        # Get demographics for interpretation
        print("\n👤 Patient Demographics (for result interpretation):")
        try:
            age = int(input("Age: "))
            height = float(input("Height (cm): "))
            gender = input("Gender (M/F): ").upper()
            spirometer.set_demographics(age, height, gender)
        except:
            print("Using default demographics")

        # Test instructions
        print("\n📋 TEST INSTRUCTIONS:")
        print("1. Sit up straight")
        print("2. Take the deepest breath possible")
        print("3. Blow as hard and fast as you can")
        print("4. Continue blowing until completely empty")

        input("\nPress Enter when ready to start...")

        # Perform test
        await spirometer.start_test()

        print("\n🔄 Test in progress... (blow now!)")

        # Monitor for completion
        timeout = 30
        start_time = time.time()

        while time.time() - start_time < timeout:
            await asyncio.sleep(0.5)
            if spirometer.test_complete:
                break

        # Get results
        await asyncio.sleep(2)
        results = await spirometer.get_results()

        # Display results
        print("\n📊 TEST RESULTS:")
        print("="*30)
        for key, value in results.items():
            print(f"{key}: {value}")

        spirometer.print_summary()
        spirometer.close_csv()
        await spirometer.disconnect()

        return results

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None


async def debug_raw_data():
    """Debug mode - capture all raw BLE data."""
    print("🔍 RAW DATA DEBUG MODE")
    print("="*30)

    spirometer = ComprehensiveSpirometer(enable_csv=True)

    try:
        await spirometer.connect()
        spirometer.init_csv_logging()
        await spirometer.setup_all_notifications()

        print("\n🔄 Monitoring all BLE characteristics...")
        print("This will capture data during a spirometry test.")
        print("\nTime    | Char      | Hex Data | Bytes | Interpretations")
        print("-" * 80)

        input("Press Enter to start test and begin monitoring...")

        # Start the test
        await spirometer.start_test()
        print("✅ Test started - now perform a strong blow!")

        # Give clear instructions
        print("\n📋 BLOW INSTRUCTIONS:")
        print("1. Take the deepest breath possible")
        print("2. Blow as hard and fast as you can into the device")
        print("3. Continue until completely empty")
        print("4. Watch for DEVICE_INFO data changes below:")
        print()

        # Extended monitoring with more frequent checks
        timeout = 60  # Longer timeout
        start_time = time.time()
        last_data_time = start_time

        while time.time() - start_time < timeout:
            await asyncio.sleep(0.05)  # Higher frequency monitoring

            # Check if we got recent data
            if spirometer.raw_data_log:
                last_entry = spirometer.raw_data_log[-1]
                if last_entry['timestamp'] > last_data_time:
                    last_data_time = last_entry['timestamp']

            if spirometer.test_complete:
                print("\n✅ Test completed via status notification")
                break

        # Check if we got any meaningful data
        device_info_data = [entry for entry in spirometer.raw_data_log
                            if entry['char_name'] == 'DEVICE_INFO']

        if len(device_info_data) > 1:
            print(
                f"\n🎯 Captured {len(device_info_data)} DEVICE_INFO data points!")
            print("This characteristic is likely the raw turbine sensor data!")
        elif len(device_info_data) == 1:
            print(f"\n⚠️  Only got 1 DEVICE_INFO data point. Try blowing harder/longer.")
        else:
            print(f"\n❌ No DEVICE_INFO data captured. Check device connection.")

        await asyncio.sleep(3)

        spirometer.print_summary()
        spirometer.close_csv()
        await spirometer.disconnect()

        print("\n✅ Debug session complete!")
        print("📁 Check the CSV file for detailed analysis")

    except Exception as e:
        print(f"❌ Debug failed: {e}")


def plot_csv_data():
    """Plot data from existing CSV files."""
    print("📈 PLOT MODE")
    print("="*20)

    # Find CSV files
    csv_files = glob.glob("spirometer_data_*.csv")
    if not csv_files:
        print("❌ No CSV files found. Run debug mode first.")
        return

    # Use latest file
    csv_files.sort(key=os.path.getmtime, reverse=True)
    csv_file = csv_files[0]
    print(f"📁 Using: {csv_file}")

    try:
        import pandas as pd
        import matplotlib.pyplot as plt

        df = pd.read_csv(csv_file)
        print(f"📊 Loaded {len(df)} data points")

        # Plot each characteristic
        characteristics = df['char_name'].unique()

        fig, axes = plt.subplots(len(characteristics),
                                 1, figsize=(12, 3*len(characteristics)))
        if len(characteristics) == 1:
            axes = [axes]

        for i, char in enumerate(characteristics):
            char_data = df[df['char_name'] == char]
            ax = axes[i]

            ax.set_title(f'{char} - Raw Data Over Time')
            ax.plot(char_data['relative_time'],
                    char_data['byte_0'], 'b-', label='Raw')
            ax.plot(char_data['relative_time'],
                    char_data['scaled_div10'], 'r--', label='÷10')
            ax.plot(char_data['relative_time'],
                    char_data['scaled_div100'], 'g--', label='÷100')

            ax.set_xlabel('Time (seconds)')
            ax.set_ylabel('Value')
            ax.legend()
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    except ImportError:
        print("❌ matplotlib/pandas not available. Install with:")
        print("pip install matplotlib pandas")
    except Exception as e:
        print(f"❌ Plot failed: {e}")


async def monitor_device():
    """Monitor device status and characteristics."""
    print("👁️  MONITOR MODE")
    print("="*20)

    spirometer = ComprehensiveSpirometer(enable_csv=False)

    try:
        await spirometer.connect()
        await spirometer.dump_all_characteristics()
        await spirometer.setup_all_notifications()

        print("\n🔄 Monitoring device... (Ctrl+C to stop)")

        while True:
            await asyncio.sleep(1)

    except KeyboardInterrupt:
        print("\n⏹️  Monitoring stopped")
    except Exception as e:
        print(f"❌ Monitor failed: {e}")
    finally:
        await spirometer.disconnect()


def main():
    """Main function with mode selection."""
    print("🫁 MIR Smart One Spirometer Debug Tool")
    print("="*45)

    # Get mode from command line or prompt
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    else:
        print("\nAvailable modes:")
        print("1. test    - Run spirometry test with results")
        print("2. debug   - Capture raw BLE data to CSV")
        print("3. monitor - Monitor device status")
        print("4. plot    - Plot existing CSV data")

        mode = input(
            "\nSelect mode (test/debug/monitor/plot) [test]: ").lower() or "test"

    try:
        if mode == "test":
            results = asyncio.run(run_spirometry_test())
        elif mode == "debug":
            asyncio.run(debug_raw_data())
        elif mode == "monitor":
            asyncio.run(monitor_device())
        elif mode == "plot":
            plot_csv_data()
        else:
            print(f"❌ Unknown mode: {mode}")

    except KeyboardInterrupt:
        print("\n⏹️  Cancelled")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
