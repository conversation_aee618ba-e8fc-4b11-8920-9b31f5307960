#!/usr/bin/env python3
"""
MIR Smart One Communication Diagnostic Script

This script investigates why the device is not emitting notifications after test start.
Focus areas:
1. Device pairing/lock status
2. Test command validation
3. Status characteristic monitoring
4. Alternative command sequences
5. Notification subscription verification
"""

import asyncio
import struct
import time
import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from code.spirometry.mir_smart_one import MirSmartOne
    print("✅ Successfully imported MirSmartOne class")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class DeviceDiagnostic(MirSmartOne):
    """Enhanced diagnostic class to investigate communication issues."""
    
    def __init__(self):
        super().__init__(debug=True)
        self.notification_count = 0
        self.last_notification_time = None
        self.status_history = []
        
    def fev1_handler(self, sender, data):
        """Track FEV1 notifications."""
        self.notification_count += 1
        self.last_notification_time = time.time()
        print(f"🔔 FEV1 NOTIFICATION #{self.notification_count}: {data.hex()} at {time.time():.2f}")
        super().fev1_handler(sender, data)
    
    def fvc_handler(self, sender, data):
        """Track FVC notifications."""
        self.notification_count += 1
        self.last_notification_time = time.time()
        print(f"🔔 FVC NOTIFICATION #{self.notification_count}: {data.hex()} at {time.time():.2f}")
        super().fvc_handler(sender, data)
    
    def pef_handler(self, sender, data):
        """Track PEF notifications."""
        self.notification_count += 1
        self.last_notification_time = time.time()
        print(f"🔔 PEF NOTIFICATION #{self.notification_count}: {data.hex()} at {time.time():.2f}")
        super().pef_handler(sender, data)
    
    def ratio_handler(self, sender, data):
        """Track ratio notifications."""
        self.notification_count += 1
        self.last_notification_time = time.time()
        print(f"🔔 RATIO NOTIFICATION #{self.notification_count}: {data.hex()} at {time.time():.2f}")
        super().ratio_handler(sender, data)
    
    def status_handler(self, sender, data):
        """Track status notifications with history."""
        self.notification_count += 1
        self.last_notification_time = time.time()
        
        if len(data) >= 1:
            status = data[0]
            timestamp = time.time()
            self.status_history.append((timestamp, status, data.hex()))
            print(f"🔔 STATUS NOTIFICATION #{self.notification_count}: {data.hex()} (status={status}) at {timestamp:.2f}")
        
        super().status_handler(sender, data)
    
    async def read_status_characteristic(self, label=""):
        """Read and display current status."""
        try:
            data = await self.client.read_gatt_char(self.status_char)
            status = data[0] if len(data) >= 1 else None
            print(f"📊 Status {label}: {data.hex()} (status={status})")
            return status
        except Exception as e:
            print(f"❌ Error reading status {label}: {e}")
            return None
    
    async def try_alternative_commands(self):
        """Try different command sequences to trigger test mode."""
        print("\n🔧 TRYING ALTERNATIVE COMMAND SEQUENCES")
        print("="*50)
        
        commands_to_try = [
            (b'\x00', "Reset/Stop command"),
            (b'\x01', "Standard start command"),
            (b'\x02', "Get last result command"),
            (b'\x03', "Alternative start command"),
            (b'\xFF', "Max value command"),
            (b'\x01\x00', "Start with parameter"),
            (b'\x00\x01', "Prep then start"),
        ]
        
        for cmd, description in commands_to_try:
            print(f"\n🧪 Trying: {description} ({cmd.hex()})")
            
            # Read status before
            await self.read_status_characteristic("before")
            
            try:
                await self.client.write_gatt_char(self.command_char, cmd)
                print(f"✅ Command sent successfully")
                
                # Wait and check for notifications
                await asyncio.sleep(2)
                
                # Read status after
                await self.read_status_characteristic("after")
                
                if self.notification_count > 0:
                    print(f"🎉 SUCCESS! Got {self.notification_count} notifications")
                    return True
                    
            except Exception as e:
                print(f"❌ Command failed: {e}")
        
        return False
    
    async def verify_notification_subscriptions(self):
        """Verify that notifications are properly subscribed."""
        print("\n🔍 VERIFYING NOTIFICATION SUBSCRIPTIONS")
        print("="*50)
        
        characteristics = [
            (self.fev1_char, "FEV1"),
            (self.fvc_char, "FVC"), 
            (self.pef_char, "PEF"),
            (self.fev1_fvc_char, "FEV1/FVC"),
            (self.status_char, "Status")
        ]
        
        for char_uuid, name in characteristics:
            try:
                # Check if characteristic exists and has notify property
                char = None
                for service in self.client.services:
                    for c in service.characteristics:
                        if c.uuid.lower() == char_uuid.lower():
                            char = c
                            break
                
                if char:
                    print(f"📡 {name} ({char_uuid}):")
                    print(f"   Properties: {char.properties}")
                    
                    if 'notify' in char.properties:
                        print(f"   ✅ Supports notifications")
                        
                        # Try to read current value if readable
                        if 'read' in char.properties:
                            try:
                                data = await self.client.read_gatt_char(char_uuid)
                                print(f"   Current value: {data.hex()}")
                            except Exception as e:
                                print(f"   Read error: {e}")
                    else:
                        print(f"   ❌ Does NOT support notifications")
                else:
                    print(f"❌ {name} characteristic not found!")
                    
            except Exception as e:
                print(f"❌ Error checking {name}: {e}")
    
    async def monitor_device_state(self, duration=10):
        """Monitor device for any spontaneous activity."""
        print(f"\n👁️ MONITORING DEVICE STATE FOR {duration} SECONDS")
        print("="*50)
        print("Watching for any spontaneous notifications or status changes...")
        
        start_time = time.time()
        initial_count = self.notification_count
        
        while time.time() - start_time < duration:
            await asyncio.sleep(0.5)
            
            # Check if we got any notifications
            if self.notification_count > initial_count:
                print(f"🔔 Spontaneous activity detected! {self.notification_count - initial_count} notifications")
                return True
        
        print("😴 No spontaneous activity detected")
        return False


async def diagnose_device_communication():
    """Main diagnostic function."""
    print("🔍 MIR Smart One Communication Diagnostic")
    print("="*60)
    print("Investigating why device doesn't emit notifications after test start")
    print()
    
    diagnostic = DeviceDiagnostic()
    
    try:
        # Step 1: Connect
        print("🔗 STEP 1: CONNECTING TO DEVICE")
        print("="*40)
        await diagnostic.connect()
        
        # Step 2: Verify characteristics and subscriptions
        await diagnostic.verify_notification_subscriptions()
        
        # Step 3: Set up notifications
        print("\n📡 STEP 2: SETTING UP NOTIFICATIONS")
        print("="*40)
        await diagnostic.setup_notifications()
        print("✅ Notifications enabled")
        
        # Step 4: Read initial status
        print("\n📊 STEP 3: READING INITIAL DEVICE STATE")
        print("="*40)
        initial_status = await diagnostic.read_status_characteristic("initial")
        
        # Step 5: Monitor for spontaneous activity
        await diagnostic.monitor_device_state(5)
        
        # Step 6: Try standard test command
        print("\n🧪 STEP 4: TRYING STANDARD TEST COMMAND")
        print("="*40)
        
        print("Sending standard start command (b'\\x01')...")
        await diagnostic.read_status_characteristic("before start")
        
        try:
            await diagnostic.client.write_gatt_char(diagnostic.command_char, b'\x01')
            print("✅ Start command sent")
        except Exception as e:
            print(f"❌ Start command failed: {e}")
        
        # Monitor for 10 seconds
        print("\n⏱️ Monitoring for 10 seconds after start command...")
        start_time = time.time()
        
        while time.time() - start_time < 10:
            await asyncio.sleep(1)
            elapsed = time.time() - start_time
            print(f"[{elapsed:4.1f}s] Notifications: {diagnostic.notification_count}, Last: {diagnostic.last_notification_time or 'None'}")
            
            # Check status periodically
            if int(elapsed) % 3 == 0:
                await diagnostic.read_status_characteristic(f"at {elapsed:.0f}s")
        
        # Step 7: Try alternative commands if standard didn't work
        if diagnostic.notification_count == 0:
            print("\n❌ No notifications received from standard command")
            success = await diagnostic.try_alternative_commands()
            
            if not success:
                print("\n❌ No alternative commands worked either")
        
        # Step 8: Final status check
        print("\n📊 FINAL DEVICE STATE")
        print("="*30)
        final_status = await diagnostic.read_status_characteristic("final")
        
        print(f"\nNotification Summary:")
        print(f"  Total notifications: {diagnostic.notification_count}")
        print(f"  Status history: {diagnostic.status_history}")
        print(f"  Initial status: {initial_status}")
        print(f"  Final status: {final_status}")
        
        # Analysis
        print("\n🔍 ANALYSIS")
        print("="*20)
        
        if diagnostic.notification_count == 0:
            print("❌ CRITICAL: Device sent NO notifications")
            print("\nPossible causes:")
            print("1. 🔒 Device is locked/paired to official app")
            print("2. 🔧 Wrong command sequence or parameters")
            print("3. 📶 Device requires specific initialization")
            print("4. 🔋 Device is in sleep/power-save mode")
            print("5. 🚫 Third-party access is blocked")
            
            print("\nRecommended actions:")
            print("• Try unpairing from official app")
            print("• Power cycle the device")
            print("• Check if device has a 'pairing mode'")
            print("• Analyze official app's BLE traffic")
            
        else:
            print(f"✅ Device sent {diagnostic.notification_count} notifications")
            print("Communication is working!")
        
        # Disconnect
        await diagnostic.disconnect()
        
    except Exception as e:
        print(f"❌ Diagnostic error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🩺 MIR Smart One Communication Diagnostic Tool")
    print("This tool investigates why the device doesn't send notifications")
    print()
    
    try:
        asyncio.run(diagnose_device_communication())
    except KeyboardInterrupt:
        print("\n⏹️ Diagnostic cancelled")
    
    print("\n" + "="*60)
    print("🎯 DIAGNOSTIC COMPLETE")
    print("="*60)
    print("Review the analysis above to understand the communication issue.")
    print("If no notifications were received, the device may be:")
    print("• Locked to the official app")
    print("• Requiring a specific initialization sequence")
    print("• In a protected/encrypted mode")
    print("• Needing a firmware-specific command protocol")
