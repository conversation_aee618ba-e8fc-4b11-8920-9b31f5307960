#!/usr/bin/env python3
"""
Example client for the RespiraSense AI API
This script demonstrates how to interact with the Flask API endpoints
"""

import requests
import json
import time

# API base URL
API_BASE_URL = "http://localhost:5000"

def check_api_health():
    """Check if the API is running and healthy"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            print("API Health Check:")
            print(json.dumps(response.json(), indent=2))
            return True
        else:
            print(f"API health check failed with status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("Could not connect to the API. Make sure the server is running.")
        return False

def get_temperature():
    """Get body temperature from the API"""
    try:
        response = requests.get(f"{API_BASE_URL}/temperature")
        if response.status_code == 200:
            print("\nTemperature Reading:")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"Failed to get temperature. Status code: {response.status_code}")
            print(response.text)
    except requests.exceptions.RequestException as e:
        print(f"Error getting temperature: {str(e)}")

def get_breathing_rate():
    """Get breathing rate from the API"""
    try:
        response = requests.get(f"{API_BASE_URL}/breathing_rate")
        if response.status_code == 200:
            print("\nBreathing Rate Reading:")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"Failed to get breathing rate. Status code: {response.status_code}")
            print(response.text)
    except requests.exceptions.RequestException as e:
        print(f"Error getting breathing rate: {str(e)}")

def perform_spirometry():
    """Perform a spirometry test through the API"""
    try:
        print("\nStarting spirometry test (this may take a minute)...")
        response = requests.get(f"{API_BASE_URL}/spirometry")
        if response.status_code == 200:
            print("\nSpirometry Test Results:")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"Failed to perform spirometry test. Status code: {response.status_code}")
            print(response.text)
    except requests.exceptions.RequestException as e:
        print(f"Error performing spirometry test: {str(e)}")

def predict_copd():
    """Make a COPD prediction using the API"""
    # Sample data for prediction
    sample_data = {
        'age': 65,
        'gen': 1,  # female
        'ht': 165,
        'wt': 70,
        'smoke_hist': 1,  # former smoker
        'smoke_rt': 15,
        'fev1': 1.8,
        'fvc': 2.9,
        'pefr': 280,
        'hr': 82,
        'spo2': 94,
        'temp': 36.8,
        'wheeze': 1,
        'cough': 1,
        'sob': 1,
        'sputum': 1,
        'pectoriloquy': 0,
        'exp_biomass': 0,
        'exp_occ': 1,
        'fam_hist': 0
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/predict",
            json=sample_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("\nCOPD Prediction Results:")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"Failed to get prediction. Status code: {response.status_code}")
            print(response.text)
    except requests.exceptions.RequestException as e:
        print(f"Error making prediction: {str(e)}")

def main():
    """Main function to demonstrate API usage"""
    print("RespiraSense AI API Client Example\n")
    
    # Check if API is healthy
    if not check_api_health():
        return
    
    # Menu loop
    while True:
        print("\nOptions:")
        print("1. Get Temperature")
        print("2. Get Breathing Rate")
        print("3. Perform Spirometry Test")
        print("4. Predict COPD Risk")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ")
        
        if choice == '1':
            get_temperature()
        elif choice == '2':
            get_breathing_rate()
        elif choice == '3':
            perform_spirometry()
        elif choice == '4':
            predict_copd()
        elif choice == '5':
            print("Exiting...")
            break
        else:
            print("Invalid choice. Please try again.")
        
        time.sleep(1)

if __name__ == "__main__":
    main()
