#!/usr/bin/env python3
"""
MIR Smart One Status Monitoring Script

This script specifically monitors the status characteristic before and after
test commands to determine if the device is actually entering test mode.

Key Focus:
1. Read status before test start
2. Send test command
3. Monitor status changes
4. Verify if test mode is triggered
"""

import asyncio
import time
import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from code.spirometry.mir_smart_one import MirSmartOne
    print("✅ Successfully imported MirSmartOne class")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


async def monitor_status_during_test():
    """Monitor status characteristic during test sequence."""
    
    print("📊 MIR Smart One Status Monitor")
    print("="*50)
    print("This script monitors the status characteristic to verify")
    print("if the device actually enters test mode after commands.")
    print()
    
    spirometer = MirSmartOne(debug=True)
    
    try:
        # Connect
        print("🔗 Connecting to device...")
        await spirometer.connect()
        
        # Set up notifications (including status)
        print("📡 Setting up notifications...")
        await spirometer.setup_notifications()
        
        print("\n" + "="*50)
        print("📊 STATUS MONITORING SEQUENCE")
        print("="*50)
        
        # Function to read and display status
        async def read_status(label):
            try:
                data = await spirometer.client.read_gatt_char(spirometer.status_char)
                if len(data) >= 1:
                    status = data[0]
                    print(f"📊 Status {label}: {data.hex()} (decimal: {status})")
                    return status
                else:
                    print(f"📊 Status {label}: {data.hex()} (empty)")
                    return None
            except Exception as e:
                print(f"❌ Error reading status {label}: {e}")
                return None
        
        # 1. Read initial status
        print("\n1️⃣ INITIAL STATUS")
        initial_status = await read_status("(initial)")
        
        # 2. Wait a moment
        await asyncio.sleep(2)
        
        # 3. Read status before test
        print("\n2️⃣ PRE-TEST STATUS")
        pre_test_status = await read_status("(before test)")
        
        # 4. Send start test command
        print("\n3️⃣ SENDING START TEST COMMAND")
        try:
            await spirometer.client.write_gatt_char(spirometer.command_char, b'\x01')
            print("✅ Start test command (b'\\x01') sent successfully")
        except Exception as e:
            print(f"❌ Failed to send start command: {e}")
            return
        
        # 5. Monitor status changes over time
        print("\n4️⃣ MONITORING STATUS CHANGES")
        print("Time | Status | Hex  | Change")
        print("-" * 35)
        
        start_time = time.time()
        last_status = pre_test_status
        
        for i in range(20):  # Monitor for 20 seconds
            await asyncio.sleep(1)
            elapsed = time.time() - start_time
            
            current_status = await read_status(f"(+{elapsed:.0f}s)")
            
            if current_status != last_status:
                change = "📈 CHANGED!" if current_status != last_status else ""
                print(f"{elapsed:4.0f}s | {current_status:6} | {current_status:02x}   | {change}")
                last_status = current_status
            else:
                print(f"{elapsed:4.0f}s | {current_status:6} | {current_status:02x}   | (same)")
        
        # 6. Try get last result command
        print("\n5️⃣ TRYING GET LAST RESULT COMMAND")
        try:
            await spirometer.client.write_gatt_char(spirometer.command_char, b'\x02')
            print("✅ Get last result command (b'\\x02') sent")
            
            await asyncio.sleep(3)
            final_status = await read_status("(after get result)")
            
        except Exception as e:
            print(f"❌ Failed to send get result command: {e}")
        
        # 7. Analysis
        print("\n" + "="*50)
        print("📈 STATUS ANALYSIS")
        print("="*50)
        
        print(f"Initial status:   {initial_status}")
        print(f"Pre-test status:  {pre_test_status}")
        print(f"Final status:     {last_status}")
        
        if initial_status == pre_test_status == last_status:
            print("\n❌ ISSUE: Status never changed")
            print("   → Device may not be responding to commands")
            print("   → Test mode was likely NOT triggered")
            print("   → Device might be locked or require different commands")
        else:
            print("\n✅ Status changed during sequence")
            print("   → Device is responding to commands")
            print("   → Check if changes correspond to test phases")
        
        # Common status interpretations
        print(f"\n🔍 Status Code Interpretation:")
        status_meanings = {
            0: "Idle/Ready",
            1: "Test in progress", 
            2: "Test complete",
            3: "Error state",
            255: "Unknown/Error"
        }
        
        for code, meaning in status_meanings.items():
            if code in [initial_status, pre_test_status, last_status]:
                print(f"   {code}: {meaning} ⭐")
            else:
                print(f"   {code}: {meaning}")
        
        # Disconnect
        await spirometer.disconnect()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


async def test_all_commands():
    """Test various commands and monitor status response."""
    
    print("\n🧪 TESTING ALL POSSIBLE COMMANDS")
    print("="*50)
    
    spirometer = MirSmartOne(debug=True)
    
    try:
        await spirometer.connect()
        await spirometer.setup_notifications()
        
        commands = [
            (b'\x00', "Reset/Stop"),
            (b'\x01', "Start Test"),
            (b'\x02', "Get Last Result"),
            (b'\x03', "Command 3"),
            (b'\x04', "Command 4"),
            (b'\x05', "Command 5"),
            (b'\xFF', "Max Value"),
        ]
        
        for cmd, description in commands:
            print(f"\n🧪 Testing: {description} ({cmd.hex()})")
            
            # Read status before
            try:
                data = await spirometer.client.read_gatt_char(spirometer.status_char)
                before_status = data[0] if len(data) >= 1 else None
                print(f"   Before: {before_status}")
            except:
                before_status = None
            
            # Send command
            try:
                await spirometer.client.write_gatt_char(spirometer.command_char, cmd)
                print(f"   ✅ Command sent")
                
                # Wait and check status
                await asyncio.sleep(2)
                
                data = await spirometer.client.read_gatt_char(spirometer.status_char)
                after_status = data[0] if len(data) >= 1 else None
                print(f"   After:  {after_status}")
                
                if before_status != after_status:
                    print(f"   🎉 STATUS CHANGED! {before_status} → {after_status}")
                else:
                    print(f"   😐 No status change")
                    
            except Exception as e:
                print(f"   ❌ Command failed: {e}")
        
        await spirometer.disconnect()
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("📊 MIR Smart One Status Monitoring Tool")
    print("This tool monitors status changes to verify test mode activation")
    print()
    
    choice = input("Choose: (1) Monitor status during test, (2) Test all commands: ").strip()
    
    try:
        if choice == "2":
            asyncio.run(test_all_commands())
        else:
            asyncio.run(monitor_status_during_test())
            
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring cancelled")
    
    print("\n" + "="*50)
    print("🎯 KEY INSIGHTS")
    print("="*50)
    print("If status never changes:")
    print("• Device is not responding to commands")
    print("• May be locked to official app")
    print("• Wrong command protocol")
    print("• Device in protected mode")
    print()
    print("If status changes but no notifications:")
    print("• Commands work but data transmission doesn't")
    print("• May need additional initialization")
    print("• Notifications may be disabled/filtered")
    print("• Different data retrieval method needed")
