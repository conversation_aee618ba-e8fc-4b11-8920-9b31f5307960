 python recreate_handshake.py                                                                                                    ─╯
✅ Successfully imported MirSmartOne class
🤝 MIR Smart One Handshake Recreation Tool
This tool attempts to recreate the official app's handshake

⚠️ PREPARATION CHECKLIST:
1. ✅ Device unpaired from official app
2. ✅ Device power cycled
3. ✅ Bluetooth cache cleared
4. ✅ Device is in range and powered on

Ready to attempt handshake recreation? (y/n): y
🤝 MIR Smart One Handshake Recreation
============================================================
Attempting to recreate the authentication handshake
that the official app uses to unlock data communication.

🔗 Connecting to device...
Scanning for MIR Smart One device: CF:C3:A0:A2:BE:15
Connecting to SO-008-A043160
Connected!
Discovering services...
Service: 00001801-0000-1000-8000-00805f9b34fb
Service: 0000fe59-0000-1000-8000-00805f9b34fb
  Characteristic: 8ec90003-f315-4f60-9fb8-838830daea50, Properties: ['write', 'indicate']
Service: 0000180f-0000-1000-8000-00805f9b34fb
  Characteristic: 00002a19-0000-1000-8000-00805f9b34fb, Properties: ['read', 'notify']
Service: 7f04f3f0-b665-11e3-a5e2-0800200c9a66
  Characteristic: 7d32c0f0-bef5-11e3-b1b6-0800200c9a66, Properties: ['read', 'notify']
  Characteristic: 1dcec130-b668-11e3-a5e2-0800200c9a66, Properties: ['read', 'notify']
  Characteristic: f9f84153-b667-11e3-a5e2-0800200c9a66, Properties: ['read', 'notify']
  Characteristic: f9f84150-b667-11e3-a5e2-0800200c9a66, Properties: ['read', 'notify']
  Characteristic: 92b403f0-b665-11e3-a5e2-0800200c9a66, Properties: ['write-without-response', 'write']
  Characteristic: 2d417c80-b667-11e3-a5e2-0800200c9a66, Properties: ['read', 'notify']
  Characteristic: f9f84152-b667-11e3-a5e2-0800200c9a66, Properties: ['read', 'notify']
  Characteristic: f9f84151-b667-11e3-a5e2-0800200c9a66, Properties: ['read', 'notify']
Service: 00001800-0000-1000-8000-00805f9b34fb
  Characteristic: 00002a00-0000-1000-8000-00805f9b34fb, Properties: ['read', 'write']
  Characteristic: 00002a04-0000-1000-8000-00805f9b34fb, Properties: ['read']
  Characteristic: 00002aa6-0000-1000-8000-00805f9b34fb, Properties: ['read']
  Characteristic: 00002a01-0000-1000-8000-00805f9b34fb, Properties: ['read']
Service: 0000180a-0000-1000-8000-00805f9b34fb
  Characteristic: 00002a29-0000-1000-8000-00805f9b34fb, Properties: ['read']
  Characteristic: 00002a25-0000-1000-8000-00805f9b34fb, Properties: ['read']
  Characteristic: 00002a24-0000-1000-8000-00805f9b34fb, Properties: ['read']
  Characteristic: 00002a26-0000-1000-8000-00805f9b34fb, Properties: ['read']
  Characteristic: 00002a28-0000-1000-8000-00805f9b34fb, Properties: ['read']
  Characteristic: 00002a27-0000-1000-8000-00805f9b34fb, Properties: ['read']
📡 Setting up notifications...
Setting up notifications...
FEV1 notifications enabled
FVC notifications enabled
PEF notifications enabled
FEV1/FVC notifications enabled
Status notifications enabled
📊 Reading baseline status...
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
Baseline status: 0000000108343530343331363000000000a0

============================================================

🔐 TRYING DEVICE SERIAL-BASED AUTHENTICATION
============================================================

🧪 Send serial (first 8 bytes)
   Command: 534f2d3030382d41
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Send serial (first 8 bytes): 534f2d3030382d41 → 0000000108343530343331363000000000a0

🧪 Send serial MD5 hash (4 bytes)
   Command: afb91431
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Send serial MD5 hash (4 bytes): afb91431 → 0000000108343530343331363000000000a0

🧪 Send serial XOR 0xAA
   Command: f9e5879a9a9287eb
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Send serial XOR 0xAA: f9e5879a9a9287eb → 0000000108343530343331363000000000a0

============================================================

🏥 TRYING MEDICAL DEVICE HANDSHAKE PATTERNS
============================================================

🧪 Medical sync pattern
   Command: aa55
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Medical sync pattern: aa55 → 0000000108343530343331363000000000a0

🧪 Reverse medical sync
   Command: 55aa
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Reverse medical sync: 55aa → 0000000108343530343331363000000000a0

🧪 Alternating pattern
   Command: ff00ff00
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Alternating pattern: ff00ff00 → 0000000108343530343331363000000000a0

🧪 Reverse alternating
   Command: 00ff00ff
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Reverse alternating: 00ff00ff → 0000000108343530343331363000000000a0

🧪 Service UUID prefix
   Command: 7f04
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Service UUID prefix: 7f04 → 0000000108343530343331363000000000a0

🧪 Characteristic UUID pattern
   Command: b665
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Characteristic UUID pattern: b665 → 0000000108343530343331363000000000a0

🧪 Command char prefix
   Command: 92b4
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Command char prefix: 92b4 → 0000000108343530343331363000000000a0

🧪 UUID suffix pattern
   Command: a5e2
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step UUID suffix pattern: a5e2 → 0000000108343530343331363000000000a0

🧪 Firmware 4.5
   Command: 0405
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Firmware 4.5: 0405 → 0000000108343530343331363000000000a0

🧪 Firmware encoded
   Command: 4500
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Firmware encoded: 4500 → 0000000108343530343331363000000000a0

🧪 App version 2.2.5
   Command: 020205
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step App version 2.2.5: 020205 → 0000000108343530343331363000000000a0

🧪 App version encoded
   Command: 2250
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step App version encoded: 2250 → 0000000108343530343331363000000000a0

🧪 Timestamp (4 bytes)
   Command: 43c53268
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Timestamp (4 bytes): 43c53268 → 0000000108343530343331363000000000a0

🧪 Timestamp (2 bytes)
   Command: 43c5
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Timestamp (2 bytes): 43c5 → 0000000108343530343331363000000000a0

🧪 Challenge pattern 1
   Command: 01234567
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Challenge pattern 1: 01234567 → 0000000108343530343331363000000000a0

🧪 Challenge pattern 2
   Command: 89abcdef
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Challenge pattern 2: 89abcdef → 0000000108343530343331363000000000a0

🧪 Challenge pattern 3
   Command: fedcba98
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Challenge pattern 3: fedcba98 → 0000000108343530343331363000000000a0

============================================================

🔄 TRYING MULTI-STEP SEQUENCES
============================================================

🔄 Multi-step: Reset → Start

🧪 Reset → Start (Step 1/2)
   Command: 00
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Reset → Start (Step 1/2): 00 → 0000000108343530343331363000000000a0

🧪 Reset → Start (Step 2/2)
   Command: 01
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Reset → Start (Step 2/2): 01 → 0000000108343530343331363000000000a0

🔄 Multi-step: Max → Reset

🧪 Max → Reset (Step 1/2)
   Command: ff
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Max → Reset (Step 1/2): ff → 0000000108343530343331363000000000a0

🧪 Max → Reset (Step 2/2)
   Command: 00
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Max → Reset (Step 2/2): 00 → 0000000108343530343331363000000000a0

🔄 Multi-step: Sync → Reverse sync

🧪 Sync → Reverse sync (Step 1/2)
   Command: aa
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Sync → Reverse sync (Step 1/2): aa → 0000000108343530343331363000000000a0

🧪 Sync → Reverse sync (Step 2/2)
   Command: 55
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Sync → Reverse sync (Step 2/2): 55 → 0000000108343530343331363000000000a0

🔄 Multi-step: Start → Get result

🧪 Start → Get result (Step 1/2)
   Command: 01
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Start → Get result (Step 1/2): 01 → 0000000108343530343331363000000000a0

🧪 Start → Get result (Step 2/2)
   Command: 02
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Start → Get result (Step 2/2): 02 → 0000000108343530343331363000000000a0

🔄 Multi-step: Reset → Sync → Start

🧪 Reset → Sync → Start (Step 1/3)
   Command: 00
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Reset → Sync → Start (Step 1/3): 00 → 0000000108343530343331363000000000a0

🧪 Reset → Sync → Start (Step 2/3)
   Command: aa
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Reset → Sync → Start (Step 2/3): aa → 0000000108343530343331363000000000a0

🧪 Reset → Sync → Start (Step 3/3)
   Command: 01
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Reset → Sync → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0

🔄 Multi-step: Max → Reset → Start

🧪 Max → Reset → Start (Step 1/3)
   Command: ff
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Max → Reset → Start (Step 1/3): ff → 0000000108343530343331363000000000a0

🧪 Max → Reset → Start (Step 2/3)
   Command: 00
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Max → Reset → Start (Step 2/3): 00 → 0000000108343530343331363000000000a0

🧪 Max → Reset → Start (Step 3/3)
   Command: 01
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Max → Reset → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0

🔄 Multi-step: Sync → Reverse → Start

🧪 Sync → Reverse → Start (Step 1/3)
   Command: aa
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Sync → Reverse → Start (Step 1/3): aa → 0000000108343530343331363000000000a0

🧪 Sync → Reverse → Start (Step 2/3)
   Command: 55
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Sync → Reverse → Start (Step 2/3): 55 → 0000000108343530343331363000000000a0

🧪 Sync → Reverse → Start (Step 3/3)
   Command: 01
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Sync → Reverse → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0

🔄 Multi-step: Service → Command → Start

🧪 Service → Command → Start (Step 1/3)
   Command: 7f04
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Service → Command → Start (Step 1/3): 7f04 → 0000000108343530343331363000000000a0

🧪 Service → Command → Start (Step 2/3)
   Command: 92b4
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Service → Command → Start (Step 2/3): 92b4 → 0000000108343530343331363000000000a0

🧪 Service → Command → Start (Step 3/3)
   Command: 01
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Service → Command → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0

🔄 Multi-step: Timestamp → Start

🧪 Timestamp → Start (Step 1/2)
   Command: 55c5
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Timestamp → Start (Step 1/2): 55c5 → 0000000108343530343331363000000000a0

🧪 Timestamp → Start (Step 2/2)
   Command: 01
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
❌ Step Timestamp → Start (Step 2/2): 01 → 0000000108343530343331363000000000a0

============================================================

📡 TRYING AUTHENTICATION VIA OTHER CHARACTERISTICS
============================================================
🧪 Trying device info characteristic
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing
🧪 Trying Nordic UART characteristic
   ❌ Nordic UART write failed: [org.bluez.Error.Failed] Operation failed with ATT error: 0xfd (Client Characteristic Configuration Descriptor Improperly Configured)

============================================================

📖 TRYING NOTIFICATION TRIGGER AUTHENTICATION
============================================================
🧪 Reading all characteristics in sequence...
[DEBUG] FEV1 raw data: ffff (2 bytes)
[DEBUG] FEV1 as uint16: 65535 (hex: ffff)
[DEBUG] Scaled options: /1000=65.535, /100=655.35, /10=6553.5
[DEBUG] FEV1 received error signal (0xFFFF)
[DEBUG] FEV1 using fallback: 0.05 L
   FEV1: ffff
[DEBUG] FVC raw data: 08 (1 bytes)
[DEBUG] FVC as byte: 8
[DEBUG] Scaled options: /100=0.08, /10=0.8, raw=8
📊 FVC: 0.8 L (scaled /10)
🫁 Test started detected - fvc changed from 0 to 0.8
   FVC: 08
[DEBUG] PEF raw data: 01 (1 bytes)
[DEBUG] PEF as byte: 1
[DEBUG] Scaled options: /100=0.01, /10=0.1, raw=1
📊 PEF: 1.0 L/s (raw)
   PEF: 01
[DEBUG] FEV1/FVC raw data: 32 (1 bytes)
[DEBUG] FEV1/FVC as byte: 50
📊 FEV1/FVC: 50% (raw)
   FEV1/FVC: 32
   Battery: ffffffffffffffffff
   Device Info: 0100
[DEBUG] Status raw data: 0000000108343530343331363000000000a0 (18 bytes)
[DEBUG] Status value: 0
[DEBUG] Status 0 - test continuing

❌ ALL HANDSHAKE METHODS FAILED
The device remains locked.

Recommendations:
1. 🕵️ Use BLE sniffer to capture official app protocol
2. 🔄 Try different device pairing states
3. 📞 Contact MIR for developer access

📋 HANDSHAKE LOG
========================================
❌ Send serial (first 8 bytes): 534f2d3030382d41 → 0000000108343530343331363000000000a0
❌ Send serial MD5 hash (4 bytes): afb91431 → 0000000108343530343331363000000000a0
❌ Send serial XOR 0xAA: f9e5879a9a9287eb → 0000000108343530343331363000000000a0
❌ Medical sync pattern: aa55 → 0000000108343530343331363000000000a0
❌ Reverse medical sync: 55aa → 0000000108343530343331363000000000a0
❌ Alternating pattern: ff00ff00 → 0000000108343530343331363000000000a0
❌ Reverse alternating: 00ff00ff → 0000000108343530343331363000000000a0
❌ Service UUID prefix: 7f04 → 0000000108343530343331363000000000a0
❌ Characteristic UUID pattern: b665 → 0000000108343530343331363000000000a0
❌ Command char prefix: 92b4 → 0000000108343530343331363000000000a0
❌ UUID suffix pattern: a5e2 → 0000000108343530343331363000000000a0
❌ Firmware 4.5: 0405 → 0000000108343530343331363000000000a0
❌ Firmware encoded: 4500 → 0000000108343530343331363000000000a0
❌ App version 2.2.5: 020205 → 0000000108343530343331363000000000a0
❌ App version encoded: 2250 → 0000000108343530343331363000000000a0
❌ Timestamp (4 bytes): 43c53268 → 0000000108343530343331363000000000a0
❌ Timestamp (2 bytes): 43c5 → 0000000108343530343331363000000000a0
❌ Challenge pattern 1: 01234567 → 0000000108343530343331363000000000a0
❌ Challenge pattern 2: 89abcdef → 0000000108343530343331363000000000a0
❌ Challenge pattern 3: fedcba98 → 0000000108343530343331363000000000a0
❌ Reset → Start (Step 1/2): 00 → 0000000108343530343331363000000000a0
❌ Reset → Start (Step 2/2): 01 → 0000000108343530343331363000000000a0
❌ Max → Reset (Step 1/2): ff → 0000000108343530343331363000000000a0
❌ Max → Reset (Step 2/2): 00 → 0000000108343530343331363000000000a0
❌ Sync → Reverse sync (Step 1/2): aa → 0000000108343530343331363000000000a0
❌ Sync → Reverse sync (Step 2/2): 55 → 0000000108343530343331363000000000a0
❌ Start → Get result (Step 1/2): 01 → 0000000108343530343331363000000000a0
❌ Start → Get result (Step 2/2): 02 → 0000000108343530343331363000000000a0
❌ Reset → Sync → Start (Step 1/3): 00 → 0000000108343530343331363000000000a0
❌ Reset → Sync → Start (Step 2/3): aa → 0000000108343530343331363000000000a0
❌ Reset → Sync → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0
❌ Max → Reset → Start (Step 1/3): ff → 0000000108343530343331363000000000a0
❌ Max → Reset → Start (Step 2/3): 00 → 0000000108343530343331363000000000a0
❌ Max → Reset → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0
❌ Sync → Reverse → Start (Step 1/3): aa → 0000000108343530343331363000000000a0
❌ Sync → Reverse → Start (Step 2/3): 55 → 0000000108343530343331363000000000a0
❌ Sync → Reverse → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0
❌ Service → Command → Start (Step 1/3): 7f04 → 0000000108343530343331363000000000a0
❌ Service → Command → Start (Step 2/3): 92b4 → 0000000108343530343331363000000000a0
❌ Service → Command → Start (Step 3/3): 01 → 0000000108343530343331363000000000a0
❌ Timestamp → Start (Step 1/2): 55c5 → 0000000108343530343331363000000000a0
❌ Timestamp → Start (Step 2/2): 01 → 0000000108343530343331363000000000a0
Device appears to have already disconnected

============================================================
🎯 HANDSHAKE RECREATION COMPLETE
============================================================
If successful, the device should now respond to commands.
If unsuccessful, BLE protocol analysis of the official app is needed.
