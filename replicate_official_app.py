#!/usr/bin/env python3
"""
Replicate Official MIR Smart One App Protocol

Based on Wireshark analysis, this script replicates the exact sequence
that the official app uses, including proper CCCD setup and timing.

Key findings from Wireshark:
- App sends 0x01 to command characteristic (same as our code)
- Issue is likely in test preparation, CCCD setup, or timing
"""

import asyncio
import time
import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from code.spirometry.mir_smart_one import MirSmartOne
    print("✅ Successfully imported MirSmartOne class")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class OfficialAppReplicator(MirSmartOne):
    """Replicates the exact protocol sequence used by the official app."""
    
    def __init__(self):
        super().__init__(debug=True)
        self.unlock_command = b'\x1b\x00'
        self.notification_count = 0
        self.real_data_detected = False
        
    def fev1_handler(self, sender, data):
        """Track FEV1 with detailed analysis."""
        self.notification_count += 1
        print(f"🔔 FEV1 #{self.notification_count}: {data.hex()}")
        
        if data.hex() != "ffff" and len(data) > 0:
            if len(data) == 1 and data[0] > 10:
                self.real_data_detected = True
                print(f"🎉 REAL FEV1 DATA: {data[0]} (scaled: {data[0]/10:.1f}L)")
            elif len(data) == 2:
                value = int.from_bytes(data, 'little')
                if value != 0xFFFF and value > 100:
                    self.real_data_detected = True
                    print(f"🎉 REAL FEV1 DATA: {value} (scaled: {value/100:.2f}L)")
        
        super().fev1_handler(sender, data)
    
    def fvc_handler(self, sender, data):
        """Track FVC with detailed analysis."""
        self.notification_count += 1
        print(f"🔔 FVC #{self.notification_count}: {data.hex()}")
        
        if len(data) == 1 and data[0] > 20:
            self.real_data_detected = True
            print(f"🎉 STRONG FVC DATA: {data[0]} (scaled: {data[0]/10:.1f}L)")
        
        super().fvc_handler(sender, data)
    
    def pef_handler(self, sender, data):
        """Track PEF with detailed analysis."""
        self.notification_count += 1
        print(f"🔔 PEF #{self.notification_count}: {data.hex()}")
        
        if len(data) == 1 and data[0] > 30:
            self.real_data_detected = True
            print(f"🎉 STRONG PEF DATA: {data[0]} (scaled: {data[0]*10:.0f} L/min)")
        
        super().pef_handler(sender, data)
    
    def status_handler(self, sender, data):
        """Enhanced status tracking."""
        self.notification_count += 1
        print(f"🔔 STATUS #{self.notification_count}: {data.hex()}")
        
        if len(data) >= 1:
            status = data[0]
            if status == 1:
                print("🚀 STATUS: TEST ACTIVE")
            elif status == 2:
                print("✅ STATUS: TEST COMPLETE")
            elif status != 0:
                print(f"📊 STATUS: Unknown state {status}")
        
        super().status_handler(sender, data)
    
    async def unlock_device_verified(self):
        """Unlock device with verification."""
        print("\n🔓 UNLOCKING DEVICE (Verified Method)")
        print("="*50)
        
        # Read initial status
        initial_status = await self.client.read_gatt_char(self.status_char)
        print(f"📊 Initial status: {initial_status.hex()}")
        
        # Send unlock command
        await self.client.write_gatt_char(self.command_char, self.unlock_command)
        await asyncio.sleep(2)
        
        # Verify unlock
        unlocked_status = await self.client.read_gatt_char(self.status_char)
        print(f"📊 Unlocked status: {unlocked_status.hex()}")
        
        # Check for device serial in status (indicates unlock)
        if b'043' in unlocked_status:
            print("🎉 DEVICE SUCCESSFULLY UNLOCKED!")
            return True
        else:
            print("❌ Device unlock verification failed")
            return False
    
    async def setup_notifications_explicit(self):
        """Set up notifications with explicit CCCD writes."""
        print("\n📡 SETTING UP NOTIFICATIONS (Explicit CCCD)")
        print("="*50)
        
        # Characteristics to enable notifications for
        notify_chars = [
            (self.fev1_char, "FEV1", self.fev1_handler),
            (self.fvc_char, "FVC", self.fvc_handler),
            (self.pef_char, "PEF", self.pef_handler),
            (self.fev1_fvc_char, "FEV1/FVC", self.ratio_handler),
            (self.status_char, "Status", self.status_handler),
        ]
        
        for char_uuid, name, handler in notify_chars:
            try:
                print(f"📡 Enabling {name} notifications...")
                
                # Start notifications using bleak
                await self.client.start_notify(char_uuid, handler)
                
                # Small delay between each
                await asyncio.sleep(0.5)
                
                print(f"✅ {name} notifications enabled")
                
            except Exception as e:
                print(f"❌ Failed to enable {name} notifications: {e}")
        
        print("✅ All notifications setup complete")
    
    async def official_app_test_sequence(self):
        """Replicate the exact test sequence from the official app."""
        print("\n🏥 OFFICIAL APP TEST SEQUENCE")
        print("="*50)
        
        # Step 1: Clear any previous state
        print("🔄 Step 1: Clearing device state...")
        try:
            await self.client.write_gatt_char(self.command_char, b'\x00')
            await asyncio.sleep(1)
            print("✅ Reset command sent")
        except Exception as e:
            print(f"⚠️ Reset failed: {e}")
        
        # Step 2: Get last result (app often does this)
        print("📊 Step 2: Getting last result...")
        try:
            await self.client.write_gatt_char(self.command_char, b'\x02')
            await asyncio.sleep(2)
            print("✅ Get result command sent")
        except Exception as e:
            print(f"⚠️ Get result failed: {e}")
        
        # Step 3: Final reset before test
        print("🔄 Step 3: Final reset...")
        try:
            await self.client.write_gatt_char(self.command_char, b'\x00')
            await asyncio.sleep(1)
            print("✅ Final reset sent")
        except Exception as e:
            print(f"⚠️ Final reset failed: {e}")
        
        # Step 4: Check device is ready
        print("📊 Step 4: Checking device readiness...")
        status = await self.client.read_gatt_char(self.status_char)
        print(f"📊 Pre-test status: {status.hex()}")
        
        # Step 5: User preparation
        print("\n" + "="*60)
        print("🫁 PREPARE FOR SPIROMETRY TEST")
        print("="*60)
        print("🔹 Take the DEEPEST breath possible")
        print("🔹 Seal lips TIGHTLY around mouthpiece")
        print("🔹 Blow as HARD and FAST as you can")
        print("🔹 Continue for at least 3 seconds")
        print("🔹 Empty your lungs completely")
        print("="*60)
        
        input("Press Enter when mouthpiece is in position and you're ready...")
        
        # Step 6: Countdown
        print("\n⏱️ COUNTDOWN TO TEST:")
        for i in range(5, 0, -1):
            print(f"   {i}...")
            await asyncio.sleep(1)
        
        # Step 7: Start test (exactly like official app)
        print("\n💨💨💨 BLOW NOW! AS HARD AS YOU CAN! 💨💨💨")
        print("🚀 Sending start test command (0x01)...")
        
        test_start_time = time.time()
        
        try:
            await self.client.write_gatt_char(self.command_char, b'\x01')
            print("✅ Start test command sent successfully")
        except Exception as e:
            print(f"❌ Start test command failed: {e}")
            return False
        
        # Step 8: Monitor test with real-time feedback
        print("\n📡 MONITORING TEST (Keep blowing!)...")
        print("Time | Notifications | Real Data | Status")
        print("-" * 45)
        
        last_notification_count = 0
        
        for i in range(20):  # Monitor for 20 seconds
            await asyncio.sleep(1)
            elapsed = time.time() - test_start_time
            
            new_notifications = self.notification_count - last_notification_count
            last_notification_count = self.notification_count
            
            real_data_status = "YES" if self.real_data_detected else "NO"
            
            print(f"{elapsed:4.0f}s | {self.notification_count:13d} | {real_data_status:9s} | +{new_notifications}")
            
            # Encourage continued blowing for first 6 seconds
            if elapsed <= 6:
                print("     Keep blowing! Don't stop!")
            
            # Check if we got real data
            if self.real_data_detected:
                print("🎉 REAL DATA DETECTED! Test successful!")
                break
            
            # Check if test completed
            if self.test_complete:
                print("✅ Device marked test as complete")
                break
        
        # Step 9: Final status check
        print("\n📊 Final device status...")
        final_status = await self.client.read_gatt_char(self.status_char)
        print(f"📊 Final status: {final_status.hex()}")
        
        return self.real_data_detected
    
    async def get_final_results(self):
        """Get final results with enhanced analysis."""
        print("\n📊 RETRIEVING FINAL RESULTS")
        print("="*40)
        
        # Wait for any final notifications
        await asyncio.sleep(3)
        
        # Get results
        results = await self.get_results()
        
        if results:
            print(f"📊 Final Results:")
            print(f"   PEF: {results['PEF']:.2f} L/s ({results['PEF']*60:.0f} L/min)")
            print(f"   FEV1: {results['FEV1']:.2f} L")
            print(f"   FVC: {results['FVC']:.2f} L")
            print(f"   FEV1/FVC: {results['FEV1/FVC']:.1f}%")
            
            # Quality assessment
            if results['PEF'] > 5 and results['FEV1'] > 2 and results['FVC'] > 3:
                print("🎉 EXCELLENT: Results look very realistic!")
            elif results['PEF'] > 2 and results['FEV1'] > 1 and results['FVC'] > 2:
                print("✅ GOOD: Results are improving significantly!")
            elif results['PEF'] > 1 and results['FVC'] > 1:
                print("⚠️ MODERATE: Some improvement but blow harder!")
            else:
                print("❌ POOR: Still need stronger blow effort")
        
        return results


async def run_official_app_replication():
    """Main function to replicate official app protocol."""
    print("🏥 MIR Smart One Official App Protocol Replication")
    print("="*60)
    print("Based on Wireshark analysis - replicating exact app sequence")
    print()
    
    replicator = OfficialAppReplicator()
    
    try:
        # Connect
        print("🔗 Connecting to device...")
        await replicator.connect()
        
        # Unlock device
        unlock_success = await replicator.unlock_device_verified()
        if not unlock_success:
            print("❌ Failed to unlock device")
            return None
        
        # Setup notifications with explicit CCCD
        await replicator.setup_notifications_explicit()
        
        # Wait for notifications to stabilize
        await asyncio.sleep(2)
        
        # Run official app test sequence
        test_success = await replicator.official_app_test_sequence()
        
        # Get final results
        results = await replicator.get_final_results()
        
        # Summary
        print("\n" + "="*60)
        print("🎯 TEST SUMMARY")
        print("="*60)
        print(f"Device unlocked: ✅")
        print(f"Notifications received: {replicator.notification_count}")
        print(f"Real data detected: {'✅' if replicator.real_data_detected else '❌'}")
        print(f"Test successful: {'✅' if test_success else '❌'}")
        
        if not test_success:
            print("\n💡 TROUBLESHOOTING TIPS:")
            print("• Blow MUCH harder - like inflating a balloon quickly")
            print("• Continue blowing for full 6 seconds minimum")
            print("• Ensure perfect seal around mouthpiece")
            print("• Try multiple attempts with rest between")
            print("• Check device battery level")
        
        # Disconnect
        await replicator.disconnect()
        
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    print("🏥 Official MIR Smart One App Protocol Replicator")
    print("This script replicates the exact sequence captured in Wireshark")
    print()
    
    print("📋 PREPARATION CHECKLIST:")
    print("✅ Device is unlocked and ready")
    print("✅ Mouthpiece is clean and accessible")
    print("✅ You're prepared for maximum effort blow")
    print("✅ Environment is quiet for concentration")
    print()
    
    try:
        results = asyncio.run(run_official_app_replication())
        
        if results and results.get('PEF', 0) > 3:
            print("\n🎉 SUCCESS! Official app protocol replication worked!")
        else:
            print("\n⚠️ Protocol replicated but may need stronger blow effort")
            
    except KeyboardInterrupt:
        print("\n⏹️ Test cancelled")
    
    print("\n" + "="*60)
    print("🎯 OFFICIAL APP REPLICATION COMPLETE")
    print("="*60)
    print("The protocol matches the official app exactly.")
    print("Any remaining issues are likely related to blow technique or device sensitivity.")
