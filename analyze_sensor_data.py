#!/usr/bin/env python3
"""
MIR Smart One Sensor Data Analyzer

This script analyzes the CSV logs from enhanced_spirometry_test.py to:
1. Identify optimal blow patterns
2. Find the best scaling factors for raw sensor data
3. Detect when real measurements vs placeholders occur
4. Provide feedback for improving blow technique
"""

import csv
import sys
import os
import matplotlib.pyplot as plt
from datetime import datetime

def load_csv_data(filename):
    """Load notification data from CSV file."""
    data = []
    try:
        with open(filename, 'r') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Convert raw_bytes string back to list
                raw_bytes_str = row['raw_bytes'].strip('[]')
                if raw_bytes_str:
                    raw_bytes = [int(x.strip()) for x in raw_bytes_str.split(',')]
                else:
                    raw_bytes = []
                
                row['raw_bytes'] = raw_bytes
                row['relative_time'] = float(row['relative_time'])
                row['attempt'] = int(row['attempt'])
                data.append(row)
        
        print(f"✅ Loaded {len(data)} notifications from {filename}")
        return data
        
    except FileNotFoundError:
        print(f"❌ File {filename} not found")
        return []
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        return []

def analyze_characteristic_patterns(data, char_name):
    """Analyze patterns for a specific characteristic."""
    char_data = [row for row in data if row['characteristic'] == char_name]
    
    if not char_data:
        print(f"❌ No data found for {char_name}")
        return
    
    print(f"\n📊 {char_name} ANALYSIS")
    print("="*40)
    
    # Unique hex patterns
    unique_patterns = set(row['hex_data'] for row in char_data)
    print(f"Unique hex patterns: {sorted(unique_patterns)}")
    
    # Value ranges for different data lengths
    by_length = {}
    for row in char_data:
        length = len(row['raw_bytes'])
        if length not in by_length:
            by_length[length] = []
        by_length[length].append(row)
    
    for length, rows in by_length.items():
        print(f"\n{length}-byte values:")
        if length == 1:
            values = [row['raw_bytes'][0] for row in rows]
            print(f"  Range: {min(values)} - {max(values)}")
            print(f"  Scaling /10: {min(values)/10:.2f} - {max(values)/10:.2f}")
            print(f"  Scaling /100: {min(values)/100:.3f} - {max(values)/100:.3f}")
        elif length == 2:
            values = [row['raw_bytes'][0] + (row['raw_bytes'][1] << 8) for row in rows]
            print(f"  Range: {min(values)} - {max(values)}")
            print(f"  Scaling /10: {min(values)/10:.2f} - {max(values)/10:.2f}")
            print(f"  Scaling /100: {min(values)/100:.2f} - {max(values)/100:.2f}")
            print(f"  Scaling /1000: {min(values)/1000:.3f} - {max(values)/1000:.3f}")
    
    # Time-based analysis
    if char_data:
        print(f"\nTiming analysis:")
        times = [row['relative_time'] for row in char_data]
        print(f"  First notification: {min(times):.2f}s")
        print(f"  Last notification: {max(times):.2f}s")
        print(f"  Total notifications: {len(char_data)}")

def detect_real_vs_placeholder_data(data):
    """Detect when real measurement data vs placeholder data occurs."""
    print(f"\n🔍 REAL vs PLACEHOLDER DATA DETECTION")
    print("="*50)
    
    # Analyze FEV1 for error patterns
    fev1_data = [row for row in data if row['characteristic'] == 'FEV1']
    error_count = sum(1 for row in fev1_data if row['hex_data'] == 'ffff')
    real_count = len(fev1_data) - error_count
    
    print(f"FEV1 Analysis:")
    print(f"  Error signals (ffff): {error_count}")
    print(f"  Potential real data: {real_count}")
    
    if real_count > 0:
        real_fev1 = [row for row in fev1_data if row['hex_data'] != 'ffff']
        print(f"  Real FEV1 patterns: {set(row['hex_data'] for row in real_fev1)}")
    
    # Analyze value progression during test
    for char_name in ['PEF', 'FVC']:
        char_data = [row for row in data if row['characteristic'] == char_name]
        if char_data:
            # Sort by time
            char_data.sort(key=lambda x: x['relative_time'])
            
            print(f"\n{char_name} progression:")
            for i, row in enumerate(char_data[:10]):  # First 10 notifications
                if row['raw_bytes']:
                    value = row['raw_bytes'][0]
                    print(f"  {row['relative_time']:5.1f}s: {value:3d} (0x{value:02x})")

def analyze_blow_quality(data):
    """Analyze blow quality based on sensor patterns."""
    print(f"\n💨 BLOW QUALITY ANALYSIS")
    print("="*40)
    
    # Group by attempt
    by_attempt = {}
    for row in data:
        attempt = row['attempt']
        if attempt not in by_attempt:
            by_attempt[attempt] = []
        by_attempt[attempt].append(row)
    
    for attempt, attempt_data in by_attempt.items():
        print(f"\nAttempt {attempt}:")
        
        # Count notifications per characteristic
        char_counts = {}
        for row in attempt_data:
            char = row['characteristic']
            char_counts[char] = char_counts.get(char, 0) + 1
        
        print(f"  Notification counts: {char_counts}")
        
        # Analyze PEF progression (indicator of blow strength)
        pef_data = [row for row in attempt_data if row['characteristic'] == 'PEF']
        if pef_data:
            pef_values = [row['raw_bytes'][0] if row['raw_bytes'] else 0 for row in pef_data]
            max_pef = max(pef_values)
            print(f"  Max PEF value: {max_pef} (scaled /10: {max_pef/10:.1f} L/s)")
            
            if max_pef < 10:
                print(f"  ⚠️ Low PEF - blow harder!")
            elif max_pef > 50:
                print(f"  ✅ Good PEF - strong blow detected")
        
        # Check test duration
        times = [row['relative_time'] for row in attempt_data]
        if times:
            duration = max(times) - min(times)
            print(f"  Test duration: {duration:.1f}s")
            
            if duration < 2:
                print(f"  ⚠️ Short test - blow longer!")
            elif duration > 6:
                print(f"  ✅ Good duration")

def suggest_optimal_scaling(data):
    """Suggest optimal scaling factors based on realistic ranges."""
    print(f"\n🎯 OPTIMAL SCALING SUGGESTIONS")
    print("="*50)
    
    # Expected realistic ranges
    expected_ranges = {
        'PEF': (300, 600),  # L/min
        'FEV1': (2.5, 4.5),  # L
        'FVC': (3.0, 5.5),   # L
    }
    
    for char_name in ['PEF', 'FVC', 'FEV1']:
        char_data = [row for row in data if row['characteristic'] == char_name and row['raw_bytes']]
        
        if not char_data:
            continue
        
        print(f"\n{char_name} scaling analysis:")
        
        # Get max value observed
        max_raw = max(row['raw_bytes'][0] if row['raw_bytes'] else 0 for row in char_data)
        
        if max_raw == 0:
            print(f"  No valid data received")
            continue
        
        expected_min, expected_max = expected_ranges.get(char_name, (1, 10))
        
        # Test different scaling factors
        scaling_factors = [1, 10, 100, 1000]
        
        for factor in scaling_factors:
            scaled_max = max_raw / factor
            
            if char_name == 'PEF':
                # PEF is often in L/s, convert to L/min for comparison
                scaled_max_lmin = scaled_max * 60
                in_range = expected_min <= scaled_max_lmin <= expected_max
                print(f"  /{factor:4d}: {scaled_max:.3f} L/s ({scaled_max_lmin:.0f} L/min) {'✅' if in_range else '❌'}")
            else:
                in_range = expected_min <= scaled_max <= expected_max
                print(f"  /{factor:4d}: {scaled_max:.3f} L {'✅' if in_range else '❌'}")

def plot_sensor_data(data, output_file="sensor_plot.png"):
    """Create plots of sensor data over time."""
    try:
        import matplotlib.pyplot as plt
        
        print(f"\n📈 CREATING SENSOR DATA PLOTS")
        print("="*40)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('MIR Smart One Sensor Data Analysis')
        
        characteristics = ['PEF', 'FVC', 'FEV1', 'STATUS']
        
        for i, char_name in enumerate(characteristics):
            ax = axes[i//2, i%2]
            
            char_data = [row for row in data if row['characteristic'] == char_name]
            
            if char_data:
                times = [row['relative_time'] for row in char_data]
                values = [row['raw_bytes'][0] if row['raw_bytes'] else 0 for row in char_data]
                
                # Group by attempt
                attempts = set(row['attempt'] for row in char_data)
                colors = ['red', 'blue', 'green', 'orange', 'purple']
                
                for j, attempt in enumerate(sorted(attempts)):
                    attempt_data = [row for row in char_data if row['attempt'] == attempt]
                    attempt_times = [row['relative_time'] for row in attempt_data]
                    attempt_values = [row['raw_bytes'][0] if row['raw_bytes'] else 0 for row in attempt_data]
                    
                    color = colors[j % len(colors)]
                    ax.plot(attempt_times, attempt_values, 'o-', color=color, 
                           label=f'Attempt {attempt}', alpha=0.7)
                
                ax.set_title(f'{char_name} Over Time')
                ax.set_xlabel('Time (seconds)')
                ax.set_ylabel('Raw Value')
                ax.legend()
                ax.grid(True, alpha=0.3)
            else:
                ax.text(0.5, 0.5, f'No {char_name} data', 
                       ha='center', va='center', transform=ax.transAxes)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        print(f"✅ Plot saved to {output_file}")
        
    except ImportError:
        print("⚠️ matplotlib not available - skipping plots")
    except Exception as e:
        print(f"❌ Error creating plots: {e}")

def main():
    """Main analysis function."""
    print("📊 MIR Smart One Sensor Data Analyzer")
    print("="*50)
    
    # Find most recent CSV file if not specified
    csv_files = [f for f in os.listdir('.') if f.startswith('spirometry_data_') and f.endswith('.csv')]
    
    if not csv_files:
        print("❌ No spirometry CSV files found in current directory")
        print("Run enhanced_spirometry_test.py first to generate data")
        return
    
    # Use most recent file
    latest_file = max(csv_files, key=os.path.getctime)
    print(f"📁 Analyzing: {latest_file}")
    
    # Load and analyze data
    data = load_csv_data(latest_file)
    
    if not data:
        return
    
    # Run all analyses
    for char_name in ['PEF', 'FVC', 'FEV1', 'STATUS']:
        analyze_characteristic_patterns(data, char_name)
    
    detect_real_vs_placeholder_data(data)
    analyze_blow_quality(data)
    suggest_optimal_scaling(data)
    
    # Create plots
    plot_sensor_data(data)
    
    print(f"\n🎯 ANALYSIS COMPLETE")
    print("="*30)
    print("💡 Key recommendations:")
    print("• Check the scaling suggestions above")
    print("• Review blow quality feedback")
    print("• Look for patterns in the sensor plots")
    print("• Focus on attempts with highest PEF values")

if __name__ == "__main__":
    main()
